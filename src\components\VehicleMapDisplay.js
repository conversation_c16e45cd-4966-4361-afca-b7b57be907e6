// VehicleMapDisplay.js - <PERSON><PERSON><PERSON>LE LIVE TRACKING FIXED VERSION (<PERSON><PERSON> and <PERSON>s moved to overlays)

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { doc, updateDoc, collection, addDoc, query, where, orderBy, limit, onSnapshot, serverTimestamp, getDocs, writeBatch, getDoc, setDoc, deleteDoc } from 'firebase/firestore';
import AlertComp from './AlertComp'; // Import the AlertComp
import { mapStyles } from './MapStyles'; // Import the extracted styles

// Fix for default markers
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// All constants moved outside component to prevent recreation
const TEAM_MEMBER_COLORS = [
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57', '#FF9FF3', 
  '#54A0FF', '#5F27CD', '#00D2D3', '#FF9F43', '#8395A7', '#FD79A8', 
  '#6C5CE7', '#A29BFE', '#FD8D07', '#26DE81', '#FC427B', '#0FB9B1', 
  '#F7B731', '#5D2AFF'
];

// Order color palette for multiple addresses
const ORDER_COLORS = [
  '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#EC4899',
  '#06B6D4', '#84CC16', '#F97316', '#6366F1', '#14B8A6', '#F59E0B'
];

const TRAIL_TIME_RANGES = [
  { label: '1H', value: 1, unit: 'hour' },
  { label: '8H', value: 8, unit: 'hour' },
  { label: '1D', value: 1, unit: 'day' },
  { label: '1W', value: 1, unit: 'week' },
  { label: '1M', value: 1, unit: 'month' }
];

// NEW: Delete time range options including ALL TIME
const DELETE_TIME_RANGES = [
  ...TRAIL_TIME_RANGES,
  { label: 'ALL', value: -1, unit: 'all' }
];

// FIXED: Day and Night tile layer configurations
const TILE_LAYER_CONFIGS = {
  day: {
    url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
    maxZoom: 19
  },
  night: {
    url: 'https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png',
    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
    maxZoom: 19
  }
};

// Driver type detection keywords
const DRIVER_TYPE_KEYWORDS = {
  tow: ['tow', 'truck', 'wrecker', 'flatbed', 'rollback', 'recovery', 'towing'],
  camera: ['camera', 'spot', 'car', 'surveillance', 'scout', 'spotter']
};

// FIXED: Maximum distance between trail points to prevent straight line artifacts (in meters)
const MAX_TRAIL_DISTANCE = 5000; // 5km
const MAX_TRAIL_SPEED = 200; // 200 mph maximum realistic speed

// MOBILE TRACKING: Enhanced geolocation options for better mobile performance
const GEOLOCATION_OPTIONS = {
  enableHighAccuracy: true,    // Force GPS usage
  timeout: 30000,              // 30 second timeout
  maximumAge: 0                // No cached positions - always fresh
};

// MOBILE TRACKING: Faster update frequency for live tracking
const LOCATION_UPDATE_INTERVAL = 5000; // 5 seconds (was 30 seconds)

// Utility functions moved outside component
const getTimeRangeMs = (range) => {
  switch (range.unit) {
    case 'hour': return range.value * 60 * 60 * 1000;
    case 'day': return range.value * 24 * 60 * 60 * 1000;
    case 'week': return range.value * 7 * 24 * 60 * 60 * 1000;
    case 'month': return range.value * 30 * 24 * 60 * 60 * 1000;
    case 'all': return -1; // Special case for all time
    default: return 60 * 60 * 1000;
  }
};

const buildFullAddress = (address, city, state, zipCode) => {
  const parts = [];
  if (address) parts.push(address);
  if (city) parts.push(city);
  if (state) parts.push(state);
  if (zipCode) parts.push(zipCode);
  return parts.join(', ');
};

// FIXED: Calculate distance between two GPS points using Haversine formula
const calculateDistance = (lat1, lng1, lat2, lng2) => {
  const R = 6371000; // Earth's radius in meters
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
            Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c; // Distance in meters
};

// FIXED: Check if two consecutive trail points should be connected
const shouldConnectTrailPoints = (point1, point2) => {
  if (!point1 || !point2) return false;
  
  const distance = calculateDistance(point1.lat, point1.lng, point2.lat, point2.lng);
  const timeDiff = Math.abs(point2.timestamp.getTime() - point1.timestamp.getTime()) / 1000; // in seconds
  
  // Don't connect if distance is too far
  if (distance > MAX_TRAIL_DISTANCE) {
    console.log(`🚫 Skipping trail connection: distance ${Math.round(distance)}m > ${MAX_TRAIL_DISTANCE}m`);
    return false;
  }
  
  // Don't connect if implied speed is unrealistic (only if time difference is significant)
  if (timeDiff > 10) { // Only check speed if more than 10 seconds between points
    const speedMph = (distance / 1000) / (timeDiff / 3600) * 0.621371; // Convert to mph
    if (speedMph > MAX_TRAIL_SPEED) {
      console.log(`🚫 Skipping trail connection: speed ${Math.round(speedMph)}mph > ${MAX_TRAIL_SPEED}mph`);
      return false;
    }
  }
  
  return true;
};

const playMoneySound = () => {
  try {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    oscillator.frequency.setValueAtTime(523.25, audioContext.currentTime);
    oscillator.frequency.setValueAtTime(659.25, audioContext.currentTime + 0.1);
    oscillator.frequency.setValueAtTime(783.99, audioContext.currentTime + 0.2);
    oscillator.frequency.setValueAtTime(1046.50, audioContext.currentTime + 0.3);
    
    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.5);
    
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.5);
  } catch (error) {
    // Audio not supported or blocked - silent fail
  }
};

// Order status mapping
const ORDER_STATUS_MAPPING = {
  'open': { label: 'Open Order', class: 'open' },
  'open-order': { label: 'Open Order', class: 'open' },
  'pending': { label: 'Pending', class: 'pending' },
  'secure': { label: 'Secured', class: 'secure' },
  'secured': { label: 'Secured', class: 'secure' },
  'pending-pickup': { label: 'Pending Pickup', class: 'pending' },
  'awaiting-pickup': { label: 'Awaiting Pickup', class: 'pending' },
  'claim': { label: 'Claim', class: 'claim' },
  'restricted': { label: 'Restricted', class: 'restricted' },
  'not-secure': { label: 'Not Secure', class: 'open' },
  'unsecure': { label: 'Unsecure', class: 'open' },
  '': { label: 'No Status', class: 'open' },
  undefined: { label: 'No Status', class: 'open' },
  null: { label: 'No Status', class: 'open' }
};

// CLEAN: Order filtering function (removed debug mode)
const shouldShowOrderOnMap = (order, currentTeamId) => {
  // Filter by team first
  if (!order.teamId || order.teamId !== currentTeamId) {
    return false;
  }

  const status = (order.status || '').toLowerCase();
  const secure = order.secure;
  
  // If secure is explicitly true, don't show it
  if (secure === true) {
    return false;
  }

  // Only hide if status is explicitly 'secure' or 'secured' AND secure is not false
  const hideStatuses = ['secure', 'secured'];
  
  if (hideStatuses.includes(status) && secure !== false) {
    return false;
  }

  return true;
};

// Generate consistent color for order
const getOrderColor = (orderId, addressIndex = 0) => {
  let hash = 0;
  for (let i = 0; i < orderId.length; i++) {
    hash = ((hash << 5) - hash + orderId.charCodeAt(i)) & 0xffffffff;
  }
  const colorIndex = Math.abs(hash) % ORDER_COLORS.length;
  return ORDER_COLORS[colorIndex];
};

// Get vehicle icon based on vehicle type
const getVehicleIconForOrder = (order) => {
  const make = (order.make || '').toLowerCase();
  const model = (order.model || '').toLowerCase();
  const vehicleType = `${make} ${model}`.toLowerCase();
  
  // Truck/SUV icon for trucks and SUVs
  if (vehicleType.includes('f-150') || vehicleType.includes('f150') || 
      vehicleType.includes('silverado') || vehicleType.includes('ram') ||
      vehicleType.includes('tundra') || vehicleType.includes('ranger') ||
      vehicleType.includes('colorado') || vehicleType.includes('tacoma') ||
      vehicleType.includes('gladiator') || vehicleType.includes('ridgeline') ||
      vehicleType.includes('truck') || vehicleType.includes('pickup') ||
      vehicleType.includes('tahoe') || vehicleType.includes('suburban') ||
      vehicleType.includes('yukon') || vehicleType.includes('escalade') ||
      vehicleType.includes('expedition') || vehicleType.includes('navigator') ||
      vehicleType.includes('sequoia') || vehicleType.includes('armada') ||
      vehicleType.includes('durango') || vehicleType.includes('bronco') ||
      vehicleType.includes('4runner') || vehicleType.includes('wrangler')) {
    return `
      <svg width="22" height="22" viewBox="0 0 24 24" fill="white" stroke="white" stroke-width="1.5">
        <rect x="3" y="11" width="18" height="8" rx="2" fill="white"/>
        <rect x="3" y="7" width="8" height="4" fill="white"/>
        <path d="M11 7 L18 7 L20 9 L20 11 L11 11 Z" fill="white"/>
        <circle cx="6" cy="16" r="2" fill="currentColor"/>
        <circle cx="18" cy="16" r="2" fill="currentColor"/>
        <path d="M8 16 L16 16" stroke="white" stroke-width="1"/>
      </svg>
    `;
  }
  
  // Motorcycle icon for motorcycles
  if (vehicleType.includes('motorcycle') || vehicleType.includes('bike') ||
      vehicleType.includes('harley') || vehicleType.includes('honda') && vehicleType.includes('cbr') ||
      vehicleType.includes('yamaha') && vehicleType.includes('r1') ||
      vehicleType.includes('kawasaki') && vehicleType.includes('ninja')) {
    return `
      <svg width="22" height="22" viewBox="0 0 24 24" fill="white" stroke="white" stroke-width="1.5">
        <circle cx="5" cy="18" r="3" fill="white"/>
        <circle cx="19" cy="18" r="3" fill="white"/>
        <path d="M8 18 L16 18" stroke="white" stroke-width="2"/>
        <path d="M12 18 L12 10 L8 6 L10 6 L14 10 L16 10" fill="white"/>
      </svg>
    `;
  }
  
  // Default car icon for cars, sedans, etc.
  return `
    <svg width="20" height="20" viewBox="0 0 24 24" fill="white" stroke="white" stroke-width="1.5">
      <path d="M5 12 L5 17 L19 17 L19 12 L17 8 L7 8 Z" fill="white"/>
      <path d="M7 8 L9 5 L15 5 L17 8" fill="white"/>
      <circle cx="8" cy="17" r="2" fill="currentColor"/>
      <circle cx="16" cy="17" r="2" fill="currentColor"/>
      <path d="M8 8 L10 6 L14 6 L16 8" fill="currentColor" opacity="0.7"/>
    </svg>
  `;
};

// NEW: Enhanced delete confirmation modal component with time range and progress
const DeleteConfirmationModal = ({ 
  isOpen, 
  onClose, 
  onConfirm, 
  type, 
  userName = null,
  isDeleting = false,
  deleteProgress = null,
  trailCount = 0,
  selectedTimeRange = null
}) => {
  if (!isOpen) return null;

  const getModalContent = () => {
    const timeRangeText = selectedTimeRange?.unit === 'all' 
      ? 'all time' 
      : `the last ${selectedTimeRange?.label || '1H'}`;
    
    switch (type) {
      case 'all':
        return {
          title: '🗑️ Delete Team Trails',
          message: `This will permanently delete ALL team member location trails from ${timeRangeText}.`,
          details: `This action affects all ${trailCount} trail records in the selected time range and cannot be undone.`,
          confirmText: `Delete ${trailCount} Trail Records`
        };
      case 'user':
        return {
          title: '🗑️ Delete Your Trail',
          message: `This will permanently delete your location trail history from ${timeRangeText}.`,
          details: `This action affects ${trailCount} of your trail records and cannot be undone.`,
          confirmText: `Delete ${trailCount} My Trail Records`
        };
      case 'single':
        return {
          title: `🗑️ Delete ${userName}'s Trails`,
          message: `This will permanently delete all location trails for ${userName} from ${timeRangeText}.`,
          details: `This action affects ${trailCount} trail records for this user and cannot be undone.`,
          confirmText: `Delete ${trailCount} Trail Records`
        };
      default:
        return {
          title: '🗑️ Delete Trails',
          message: `This will permanently delete the selected trails from ${timeRangeText}.`,
          details: `This action affects ${trailCount} trail records and cannot be undone.`,
          confirmText: `Delete ${trailCount} Trail Records`
        };
    }
  };

  const content = getModalContent();

  return (
    <div className="delete-confirmation-modal" onClick={onClose}>
      <div className="delete-confirmation-content" onClick={(e) => e.stopPropagation()}>
        <div className="delete-confirmation-header">
          <div className="delete-confirmation-title">
            {content.title}
          </div>
        </div>
        
        <div className="delete-confirmation-body">
          <div className="delete-confirmation-message">
            {content.message}
          </div>
          
          <div className="delete-confirmation-details">
            <div style={{ fontSize: '0.875rem', color: '#F59E0B', fontWeight: 'bold', marginBottom: '0.5rem' }}>
              ⚠️ Deletion Details:
            </div>
            <div style={{ fontSize: '0.8rem', color: '#E5E7EB', lineHeight: '1.4' }}>
              {content.details}
            </div>
            {selectedTimeRange && (
              <div style={{ 
                marginTop: '0.75rem', 
                padding: '0.5rem', 
                backgroundColor: '#374151', 
                borderRadius: '6px',
                fontSize: '0.75rem',
                color: '#9CA3AF' 
              }}>
                <strong>Time Range:</strong> {selectedTimeRange.unit === 'all' ? 'All historical data' : `Last ${selectedTimeRange.label} (${selectedTimeRange.value} ${selectedTimeRange.unit}${selectedTimeRange.value > 1 ? 's' : ''})`}
              </div>
            )}
          </div>
          
          {deleteProgress && (
            <div className="delete-progress">
              <div className="delete-progress-bar">
                <div 
                  className="delete-progress-fill"
                  style={{ width: `${deleteProgress.percentage}%` }}
                ></div>
              </div>
              <div className="delete-progress-text">
                {deleteProgress.message} ({deleteProgress.current}/{deleteProgress.total})
              </div>
            </div>
          )}
          
          <div className="delete-confirmation-buttons">
            <button
              className="delete-confirmation-btn confirm"
              onClick={onConfirm}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                  Deleting...
                </>
              ) : (
                content.confirmText
              )}
            </button>
            <button
              className="delete-confirmation-btn cancel"
              onClick={onClose}
              disabled={isDeleting}
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// NEW: Camera car action component
const CameraCarActionModal = ({ 
  order, 
  actionType, 
  onClose, 
  onSubmit, 
  currentUser,
  db,
  team 
}) => {
  const [notes, setNotes] = useState('');
  const [vinVerified, setVinVerified] = useState(false);
  const [photos, setPhotos] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const fileInputRef = useRef(null);
  const cameraInputRef = useRef(null);

  const actionTitles = {
    located: 'Vehicle Located',
    not_present: 'Vehicle Not Currently Present',
    blocked: 'Vehicle Blocked In'
  };

  const actionEmojis = {
    located: '✅',
    not_present: '⏰',
    blocked: '🚧'
  };

  // Handle photo upload
  const handlePhotoUpload = (event) => {
    const files = Array.from(event.target.files);
    files.forEach(file => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          setPhotos(prev => [...prev, {
            file,
            url: e.target.result,
            name: file.name
          }]);
        };
        reader.readAsDataURL(file);
      }
    });
  };

  // Handle camera capture
  const handleCameraCapture = (event) => {
    const files = Array.from(event.target.files);
    files.forEach(file => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          setPhotos(prev => [...prev, {
            file,
            url: e.target.result,
            name: `camera_${Date.now()}.jpg`
          }]);
        };
        reader.readAsDataURL(file);
      }
    });
  };

  // Remove photo
  const removePhoto = (index) => {
    setPhotos(prev => prev.filter((_, i) => i !== index));
  };

  // Handle submit
  const handleSubmit = async () => {
    if (!notes.trim()) {
      alert('Please add notes about your check-in');
      return;
    }

    if (actionType === 'located' && photos.length === 0) {
      if (!confirm('No photos added. Are you sure you want to continue without photos?')) {
        return;
      }
    }

    setIsSubmitting(true);

    try {
      await onSubmit({
        actionType,
        notes: notes.trim(),
        vinVerified: actionType === 'located' ? vinVerified : false,
        photos,
        timestamp: new Date(),
        userId: currentUser.id,
        userName: currentUser.displayName || currentUser.email?.split('@')[0] || 'Camera Car Driver'
      });

      onClose();
    } catch (error) {
      console.error('Error submitting camera car action:', error);
      alert('Error submitting action. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="camera-action-modal" onClick={onClose}>
      <div className="camera-action-content" onClick={(e) => e.stopPropagation()}>
        <div className="camera-action-header">
          <div className="camera-action-title">
            {actionEmojis[actionType]} {actionTitles[actionType]}
          </div>
          <button onClick={onClose} className="order-popup-close">
            ×
          </button>
        </div>

        <div className="camera-action-body">
          {/* Vehicle Info */}
          <div className="order-info-grid" style={{ marginBottom: '1.5rem' }}>
            <div className="order-info-item full-width">
              <div className="order-info-label">Vehicle</div>
              <div className="order-info-value">{order.year} {order.make} {order.model}</div>
            </div>
            <div className="order-info-item">
              <div className="order-info-label">VIN</div>
              <div className="order-info-value vin">{order.vin}</div>
            </div>
            <div className="order-info-item">
              <div className="order-info-label">Current Status</div>
              <div className="order-info-value">{ORDER_STATUS_MAPPING[order.status]?.label || order.status || 'Unknown'}</div>
            </div>
          </div>

          {/* VIN Verification - Only for located vehicles */}
          {actionType === 'located' && (
            <div className="camera-form-checkbox-group">
              <input
                type="checkbox"
                id="vinVerified"
                checked={vinVerified}
                onChange={(e) => setVinVerified(e.target.checked)}
                className="camera-form-checkbox"
              />
              <label htmlFor="vinVerified" className="camera-form-label" style={{ margin: 0 }}>
                ✅ VIN has been physically verified on the vehicle
              </label>
            </div>
          )}

          {/* Notes */}
          <div className="camera-form-group">
            <label className="camera-form-label">
              📝 Notes about this check-in (Required)
            </label>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="camera-form-textarea"
              placeholder={
                actionType === 'located' 
                  ? 'Describe vehicle condition, location details, accessibility for tow truck, etc.'
                  : actionType === 'not_present'
                  ? 'Describe what you found - empty parking spot, different vehicle, etc.'
                  : 'Describe what is blocking the vehicle and any potential solutions'
              }
              rows={4}
              required
            />
          </div>

          {/* Photo Section */}
          <div className="camera-photo-section">
            <h4 className="camera-form-label">📸 Photos {actionType === 'located' && '(Recommended)'}</h4>
            
            <div className="camera-photo-buttons">
              <button
                type="button"
                className="camera-photo-btn take-photo"
                onClick={() => cameraInputRef.current?.click()}
                disabled={isSubmitting}
              >
                📷 Take Photo
              </button>
              <button
                type="button"
                className="camera-photo-btn upload-photo"
                onClick={() => fileInputRef.current?.click()}
                disabled={isSubmitting}
              >
                📁 Upload Photo
              </button>
            </div>

            {/* Hidden file inputs */}
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              multiple
              onChange={handlePhotoUpload}
              style={{ display: 'none' }}
            />
            <input
              ref={cameraInputRef}
              type="file"
              accept="image/*"
              capture="environment"
              multiple
              onChange={handleCameraCapture}
              style={{ display: 'none' }}
            />

            {/* Photo previews */}
            {photos.length > 0 && (
              <div className="camera-photos-preview">
                {photos.map((photo, index) => (
                  <div key={index} className="camera-photo-preview">
                    <img src={photo.url} alt={`Photo ${index + 1}`} />
                    <button
                      type="button"
                      className="camera-photo-remove"
                      onClick={() => removePhoto(index)}
                      disabled={isSubmitting}
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="camera-action-buttons">
            <button
              type="button"
              className="camera-submit-btn submit"
              onClick={handleSubmit}
              disabled={isSubmitting || !notes.trim()}
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                  Submitting...
                </>
              ) : (
                <>
                  {actionEmojis[actionType]} Submit {actionTitles[actionType]}
                </>
              )}
            </button>
            <button
              type="button"
              className="camera-cancel-btn"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Order popup component with camera car functionality
const OrderPopup = ({ 
  order, 
  onClose, 
  onNavigate, 
  onSecure, 
  currentUser,
  db,
  team,
  userProfile,
  onTeamVehiclesUpdate 
}) => {
  const [showCameraAction, setShowCameraAction] = useState(false);
  const [cameraActionType, setCameraActionType] = useState(null);
  const [orderCheckIns, setOrderCheckIns] = useState([]);
  const [loadingCheckIns, setLoadingCheckIns] = useState(true);

  if (!order) return null;

  // Determine if current user is a camera car driver
  const isCameraCarDriver = useMemo(() => {
    if (!userProfile || !currentUser) return false;
    
    // Check driver type
    if (userProfile.driverType === 'camera') return true;
    
    // Check tags for camera car indicators
    if (userProfile.tags && Array.isArray(userProfile.tags)) {
      const hasCarTag = userProfile.tags.some(tag => 
        DRIVER_TYPE_KEYWORDS.camera.some(keyword => 
          (tag.name || '').toLowerCase().includes(keyword)
        )
      );
      if (hasCarTag) return true;
    }
    
    // Check job title
    const jobTitle = (userProfile.jobTitle || '').toLowerCase();
    if (DRIVER_TYPE_KEYWORDS.camera.some(keyword => jobTitle.includes(keyword))) {
      return true;
    }
    
    // Default assume camera car if not explicitly tow truck
    const isTowTruck = userProfile.driverType === 'tow' ||
      (userProfile.tags && Array.isArray(userProfile.tags) && 
       userProfile.tags.some(tag => 
         DRIVER_TYPE_KEYWORDS.tow.some(keyword => 
           (tag.name || '').toLowerCase().includes(keyword)
         )
       ));
    
    return !isTowTruck;
  }, [userProfile, currentUser]);

  // Load check-in history
  useEffect(() => {
    if (!db || !order.id) return;

    const loadCheckIns = async () => {
      try {
        setLoadingCheckIns(true);
        const checkInsQuery = query(
          collection(db, 'orderCheckIns'),
          where('orderId', '==', order.id),
          orderBy('timestamp', 'desc')
        );
        
        const unsubscribe = onSnapshot(checkInsQuery, (snapshot) => {
          const checkIns = [];
          snapshot.docs.forEach(doc => {
            const data = doc.data();
            checkIns.push({
              id: doc.id,
              ...data
            });
          });
          setOrderCheckIns(checkIns);
          setLoadingCheckIns(false);
        });

        return unsubscribe;
      } catch (error) {
        console.error('Error loading check-ins:', error);
        setLoadingCheckIns(false);
      }
    };

    loadCheckIns();
  }, [db, order.id]);

  // Get counters for each action type
  const getActionCounter = (actionType) => {
    return orderCheckIns.filter(checkIn => checkIn.actionType === actionType).length;
  };

  // Handle camera car actions
  const handleCameraAction = (actionType) => {
    setCameraActionType(actionType);
    setShowCameraAction(true);
  };

  // Submit camera car action
  const submitCameraAction = async (actionData) => {
    try {
      // Upload photos to storage if any
      const photoUrls = [];
      if (actionData.photos && actionData.photos.length > 0) {
        // In a real implementation, you would upload to Firebase Storage
        // For now, we'll use data URLs (not recommended for production)
        photoUrls = actionData.photos.map(photo => ({
          url: photo.url,
          name: photo.name
        }));
      }

      // Create check-in record
      const checkInData = {
        orderId: order.id,
        actionType: actionData.actionType,
        notes: actionData.notes,
        vinVerified: actionData.vinVerified,
        photos: photoUrls,
        timestamp: serverTimestamp(),
        userId: currentUser.id,
        userName: actionData.userName,
        teamId: team.id
      };

      await addDoc(collection(db, 'orderCheckIns'), checkInData);

      // If vehicle is located, update order status and add to team vehicles
      if (actionData.actionType === 'located') {
        // Update order status
        await updateDoc(doc(db, 'orders', order.id), {
          status: 'pending-pickup',
          lastCheckIn: serverTimestamp(),
          cameraCarVerified: true,
          vinVerified: actionData.vinVerified,
          updatedAt: serverTimestamp()
        });

        // Create vehicle entry for team vehicle tracker
        const weekId = `week_${new Date().getFullYear()}_${Math.ceil((new Date().getTime() - new Date(new Date().getFullYear(), 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000))}`;
        
        // Ensure week document exists
        await setDoc(
          doc(db, 'users', currentUser.id, 'vehicleWeeks', weekId),
          {
            startDate: new Date(),
            displayRange: `Week ${Math.ceil((new Date().getTime() - new Date(new Date().getFullYear(), 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000))}`,
            updatedAt: serverTimestamp()
          },
          { merge: true }
        );

        // Format address helper
        const formatAddress = (address) => {
          if (!address) return '';
          if (typeof address === 'string') return address;
          
          let parts = [];
          if (address.street) parts.push(address.street);
          
          let cityStateZip = '';
          if (address.city) cityStateZip += address.city;
          if (address.state) {
            if (cityStateZip) cityStateZip += ', ';
            cityStateZip += address.state;
          }
          if (address.zip) {
            if (cityStateZip) cityStateZip += ' ';
            cityStateZip += address.zip;
          }
          
          if (cityStateZip) parts.push(cityStateZip);
          return parts.join(', ');
        };

        const vehicleData = {
          vehicle: `${order.year} ${order.make} ${order.model}`,
          vin: order.vin,
          vinVerified: actionData.vinVerified,
          make: order.make,
          model: order.model,
          year: order.year,
          color: order.color || '',
          plateNumber: order.licensePlate || '',
          accountNumber: order.caseNumber || '',
          financier: order.customerName || '',
          address: order.addresses?.[0]?.street || '',
          city: order.addresses?.[0]?.city || '',
          state: order.addresses?.[0]?.state || '',
          zipCode: order.addresses?.[0]?.zip || '',
          fullAddress: formatAddress(order.addresses?.[0]) || '',
          position: order.addresses?.[0]?.position || order.position || null,
          date: new Date().toISOString().split('T')[0],
          status: 'PENDING PICKUP',
          notes: actionData.notes,
          images: photoUrls,
          teamMemberId: currentUser.id,
          teamMemberName: actionData.userName,
          weekId: weekId,
          weekRange: `Week ${Math.ceil((new Date().getTime() - new Date(new Date().getFullYear(), 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000))}`,
          uniqueKey: `${currentUser.id}_${weekId}_${order.id}`,
          orderId: order.id,
          fromCameraCarCheck: true,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        };

        // Add vehicle to week
        await addDoc(
          collection(db, 'users', currentUser.id, 'vehicleWeeks', weekId, 'vehicles'),
          vehicleData
        );

        // Trigger team vehicles update
        if (onTeamVehiclesUpdate && typeof onTeamVehiclesUpdate === 'function') {
          // This will be picked up by the team vehicle listeners automatically
        }

        playMoneySound();
      }

      // Update order counters
      const counterUpdates = {};
      if (actionData.actionType === 'not_present') {
        counterUpdates.notPresentCount = (order.notPresentCount || 0) + 1;
      } else if (actionData.actionType === 'blocked') {
        counterUpdates.blockedCount = (order.blockedCount || 0) + 1;
      }

      if (Object.keys(counterUpdates).length > 0) {
        await updateDoc(doc(db, 'orders', order.id), {
          ...counterUpdates,
          lastCheckIn: serverTimestamp(),
          updatedAt: serverTimestamp()
        });
      }

    } catch (error) {
      console.error('Error submitting camera car action:', error);
      throw error;
    }
  };

  const statusInfo = ORDER_STATUS_MAPPING[order.status] || { label: order.status || 'Unknown', class: 'open' };
  
  // Format date helper
  const formatDate = (dateValue) => {
    if (!dateValue) return 'N/A';
    try {
      const date = dateValue.toDate ? dateValue.toDate() : new Date(dateValue);
      return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      }).format(date);
    } catch (error) {
      return 'Invalid date';
    }
  };

  // Format address helper
  const formatAddress = (address) => {
    if (!address) return 'N/A';
    if (typeof address === 'string') return address;
    
    let parts = [];
    if (address.street) parts.push(address.street);
    
    let cityStateZip = '';
    if (address.city) cityStateZip += address.city;
    if (address.state) {
      if (cityStateZip) cityStateZip += ', ';
      cityStateZip += address.state;
    }
    if (address.zip) {
      if (cityStateZip) cityStateZip += ' ';
      cityStateZip += address.zip;
    }
    
    if (cityStateZip) parts.push(cityStateZip);
    return parts.join(', ');
  };

  const getVehicleImageUrl = () => {
    if (order.vehicleImage) return order.vehicleImage;
    
    const make = encodeURIComponent(order.make || '');
    const model = encodeURIComponent(order.model || '');
    const year = order.year || '';
    
    if (make && model) {
      return `https://cdn.imagin.studio/getimage?customer=img&make=${make}&modelFamily=${model}&year=${year}&angle=1`;
    }
    
    return null;
  };

  return (
    <>
      <div className="order-popup-overlay" onClick={onClose}>
        <div className="order-popup" onClick={(e) => e.stopPropagation()}>
          <div className="order-popup-header">
            <div className="order-popup-title">
              🚗 {order.year} {order.make} {order.model}
              <div className={`order-status-badge ${statusInfo.class}`}>
                {statusInfo.label}
              </div>
            </div>
            <button className="order-popup-close" onClick={onClose}>
              ×
            </button>
          </div>

          <div className="order-popup-content">
            {/* Vehicle Image */}
            {getVehicleImageUrl() ? (
              <img 
                src={getVehicleImageUrl()} 
                alt={`${order.make} ${order.model}`}
                className="order-vehicle-image"
                onError={(e) => {
                  e.target.style.display = 'none';
                  e.target.nextSibling.style.display = 'flex';
                }}
              />
            ) : null}
            
            <div className="order-vehicle-placeholder" style={{ display: getVehicleImageUrl() ? 'none' : 'flex' }}>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" viewBox="0 0 20 20" fill="currentColor">
                <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" />
                <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1v-1h3a1 1 0 001-1v-3a1 1 0 00-.7-.997l-2.099-4.199A1 1 0 0011.2 4H3zm1 2h6.308l1.7 3.4H4V6z" />
              </svg>
              <span>{order.year} {order.make} {order.model}</span>
            </div>

            {/* Vehicle Information Grid */}
            <div className="order-info-grid">
              <div className="order-info-item">
                <div className="order-info-label">Make</div>
                <div className="order-info-value">{order.make || 'N/A'}</div>
              </div>
              
              <div className="order-info-item">
                <div className="order-info-label">Model</div>
                <div className="order-info-value">{order.model || 'N/A'}</div>
              </div>
              
              <div className="order-info-item">
                <div className="order-info-label">Year</div>
                <div className="order-info-value">{order.year || 'N/A'}</div>
              </div>
              
              <div className="order-info-item">
                <div className="order-info-label">Color</div>
                <div className="order-info-value">{order.color || 'N/A'}</div>
              </div>
              
              <div className="order-info-item full-width">
                <div className="order-info-label">VIN</div>
                <div className="order-info-value vin">{order.vin || 'N/A'}</div>
              </div>
              
              {order.licensePlate && (
                <div className="order-info-item">
                  <div className="order-info-label">License Plate</div>
                  <div className="order-info-value plate">{order.licensePlate}</div>
                </div>
              )}
              
              {order.caseNumber && (
                <div className="order-info-item">
                  <div className="order-info-label">Case Number</div>
                  <div className="order-info-value">{order.caseNumber}</div>
                </div>
              )}
              
              {order.customerName && (
                <div className="order-info-item">
                  <div className="order-info-label">Customer</div>
                  <div className="order-info-value">{order.customerName}</div>
                </div>
              )}
            </div>

            {/* Addresses Section with numbered badges */}
            {order.addresses && order.addresses.length > 0 && (
              <div className="order-addresses-section">
                <div className="order-addresses-title">
                  📍 {order.addresses.length > 1 ? `Multiple Addresses (${order.addresses.length})` : 'Address'}
                </div>
                {order.addresses.map((address, index) => (
                  <div key={index} className="order-address-item" style={{
                    borderLeft: `4px solid ${getOrderColor(order.id, index)}`
                  }}>
                    <div style={{ 
                      display: 'flex', 
                      alignItems: 'center', 
                      gap: '8px',
                      marginBottom: '4px'
                    }}>
                      <div style={{
                        backgroundColor: getOrderColor(order.id, index),
                        color: 'white',
                        borderRadius: '50%',
                        width: '20px',
                        height: '20px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '12px',
                        fontWeight: 'bold'
                      }}>
                        {index + 1}
                      </div>
                      <div className="order-address-text" style={{ margin: 0 }}>
                        {formatAddress(address)}
                      </div>
                    </div>
                    {address.position && address.position.lat && address.position.lng && (
                      <div className="order-address-coords" style={{ marginLeft: '28px' }}>
                        GPS: {address.position.lat.toFixed(6)}, {address.position.lng.toFixed(6)}
                      </div>
                    )}
                  </div>
                ))}
                {order.addresses.length > 1 && (
                  <div style={{
                    marginTop: '8px',
                    padding: '8px',
                    backgroundColor: '#374151',
                    borderRadius: '6px',
                    fontSize: '12px',
                    color: '#9CA3AF'
                  }}>
                    💡 Multiple markers on map with matching colors for each address
                  </div>
                )}
              </div>
            )}

            {/* Check-in History Section */}
            {orderCheckIns.length > 0 && (
              <div className="checkin-history-section">
                <div className="checkin-history-title">
                  📋 Check-in History ({orderCheckIns.length})
                </div>
                {loadingCheckIns ? (
                  <div style={{ textAlign: 'center', padding: '1rem', color: '#9CA3AF' }}>
                    Loading check-ins...
                  </div>
                ) : (
                  <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
                    {orderCheckIns.map(checkIn => (
                      <div key={checkIn.id} className="checkin-history-item">
                        <div className="checkin-history-header">
                          <div className={`checkin-history-action ${checkIn.actionType === 'located' ? '' : checkIn.actionType}`}>
                            {checkIn.actionType === 'located' && '✅ Vehicle Located'}
                            {checkIn.actionType === 'not_present' && '⏰ Not Currently Present'}
                            {checkIn.actionType === 'blocked' && '🚧 Blocked In'}
                          </div>
                          <div className="checkin-history-time">
                            {formatDate(checkIn.timestamp)}
                          </div>
                        </div>
                        <div className="checkin-history-user">
                          By: {checkIn.userName}
                        </div>
                        {checkIn.notes && (
                          <div className="checkin-history-notes">
                            {checkIn.notes}
                          </div>
                        )}
                        {checkIn.vinVerified && (
                          <div className="checkin-history-vin">
                            ✅ VIN Verified
                          </div>
                        )}
                        {checkIn.photos && checkIn.photos.length > 0 && (
                          <div style={{ marginTop: '0.5rem', fontSize: '0.75rem', color: '#60A5FA' }}>
                            📸 {checkIn.photos.length} photo(s) attached
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Order Details */}
            {(order.details || order.notes) && (
              <div className="order-details-section">
                <div className="order-details-title">📝 Notes</div>
                <div className="order-details-text">
                  {order.details || order.notes}
                </div>
              </div>
            )}

            {/* Timestamps */}
            <div className="order-timestamp">
              Created: {formatDate(order.createdAt)}
              {order.updatedAt && (
                <> • Updated: {formatDate(order.updatedAt)}</>
              )}
              {order.securedBy && (
                <> • Secured by: {order.securedBy}</>
              )}
            </div>
          </div>

          <div className="order-popup-actions">
            {/* Camera Car Driver Actions */}
            {isCameraCarDriver && (
              <>
                <button 
                  className="order-action-btn camera-located"
                  onClick={() => handleCameraAction('located')}
                  style={{ position: 'relative' }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 16 16" fill="currentColor">
                    <path d="M10.97 4.97a.75.75 0 0 1 1.07 1.05l-3.99 4.99a.75.75 0 0 1-1.08.02L4.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093 3.473-4.425a.267.267 0 0 1 .02-.022z"/>
                  </svg>
                  Vehicle Located
                </button>
                
                <button 
                  className="order-action-btn camera-not-present"
                  onClick={() => handleCameraAction('not_present')}
                  style={{ position: 'relative' }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 16 16" fill="currentColor">
                    <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                    <path d="M5.255 5.786a.237.237 0 0 0 .241.247h.825c.138 0 .248-.113.266-.25.09-.656.54-1.134 1.342-1.134.686 0 1.314.343 1.314 1.168 0 .635-.374.927-.965 1.371-.673.489-1.206 1.06-1.168 1.987l.003.217a.25.25 0 0 0 .25.246h.811a.25.25 0 0 0 .25-.25v-.105c0-.718.273-.927 1.01-1.486.609-.463 1.244-.977 1.244-2.056 0-1.511-1.276-2.241-2.673-2.241-1.267 0-2.655.59-2.75 2.286zm1.557 5.763c0 .533.425.927 1.01.927.609 0 1.028-.394 1.028-.927 0-.552-.42-.94-1.029-.94-.584 0-1.009.388-1.009.94z"/>
                  </svg>
                  Not Present
                  {getActionCounter('not_present') > 0 && (
                    <span className="counter-badge">{getActionCounter('not_present')}</span>
                  )}
                </button>
                
                <button 
                  className="order-action-btn camera-blocked"
                  onClick={() => handleCameraAction('blocked')}
                  style={{ position: 'relative' }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 16 16" fill="currentColor">
                    <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                    <path d="M11.854 4.146a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708-.708l7-7a.5.5 0 0 1 .708 0z"/>
                  </svg>
                  Blocked In
                  {getActionCounter('blocked') > 0 && (
                    <span className="counter-badge">{getActionCounter('blocked')}</span>
                  )}
                </button>
              </>
            )}

            {/* Regular actions for tow truck drivers */}
            {(order.position || (order.addresses && order.addresses.length > 0)) && (
              <button 
                className="order-action-btn navigate"
                onClick={() => {
                  onNavigate(order);
                  onClose();
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 16 16" fill="currentColor">
                  <path d="M9.502 11a3 3 0 0 1-2.397-4.798l5.44-6.55a1 1 0 1 1 1.537 1.274l-5.01 6.53A3 3 0 0 1 9.502 11"/>
                </svg>
                Navigate
              </button>
            )}
            
            {order.status !== 'secure' && !order.secure && currentUser && !isCameraCarDriver && (
              <button 
                className="order-action-btn secure"
                onClick={() => {
                  onSecure(order);
                  onClose();
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 16 16" fill="currentColor">
                  <path d="M8 1a2 2 0 0 1 2 2v4H6V3a2 2 0 0 1 2-2zm3 6V3a3 3 0 0 0-6 0v4a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2z"/>
                </svg>
                Secure
              </button>
            )}
            
            <button className="order-action-btn close" onClick={onClose}>
              Close
            </button>
          </div>
        </div>
      </div>

      {/* Camera Car Action Modal */}
      {showCameraAction && (
        <CameraCarActionModal
          order={order}
          actionType={cameraActionType}
          onClose={() => {
            setShowCameraAction(false);
            setCameraActionType(null);
          }}
          onSubmit={submitCameraAction}
          currentUser={currentUser}
          db={db}
          team={team}
        />
      )}
    </>
  );
};

// MAIN COMPONENT - MOBILE LIVE TRACKING FIXED VERSION
const VehicleMapDisplay = ({ 
  vehicles = [], 
  teamVehicles = [], 
  orders = [], 
  db, 
  user, 
  userProfile, 
  team,
  onVehicleSelect,
  selectedVehicleId,
  onOrderSelect = null,
  selectedOrderId = null,
  onOrderSecure = null,
  onOrderNavigate = null,
  onTeamVehiclesUpdate = null,
  // NEW: Trail management functions passed from parent
  onTrailTimeRangeChange,
  onTrailToggle,
  onDeleteAllTrails,
  onDeleteUserTrails,
  onDeleteOwnTrail,
  onExportTrailMap,
  // NEW: Trail state passed from parent
  trailTimeRange,
  showTrails,
  teammateLocations,
  locationHistory,
  teammateColors,
  trailCounts,
  allTeamMembers,
  isAdmin,
  userHasTrail,
  deleteTimeRange,
  onDeleteTimeRangeChange,
  showDeleteConfirmation,
  setShowDeleteConfirmation,
  deleteProgress,
  isDeleting,
  // NEW: Chat props passed from parent
  chatMessages,
  newChatMessage,
  setNewChatMessage,
  onSendChatMessage,
  mediaToUpload,
  setMediaToUpload,
  userProfilePictures
}) => {
  // All refs with stable references
  const mapRef = useRef(null);
  const mapContainerRef = useRef(null);
  const currentLocationMarkerRef = useRef(null);
  const vehicleMarkersRef = useRef({});
  const orderMarkersRef = useRef({}); // Now stores arrays of markers per order
  const teammateMarkersRef = useRef({});
  const trailLayersRef = useRef({});
  const locationWatchIdRef = useRef(null);
  const geocodingCache = useRef({});
  const geocodingQueue = useRef([]);
  const tileLayerRef = useRef(null);
  const locationUpdateTimerRef = useRef(null);
  const mapInitializedRef = useRef(false);
  const lastVehicleCountRef = useRef(0);
  const profileLoadingTimeoutRef = useRef(null);

  // Core state management
  const [currentLocation, setCurrentLocation] = useState(null);
  const [mapReady, setMapReady] = useState(false);
  const [mapLoading, setMapLoading] = useState(true);
  const [mapError, setMapError] = useState(null);
  const [mapCenter] = useState({ lat: 41.5, lng: -88.0 });
  const [geocodingStatus, setGeocodingStatus] = useState(null);

  // MOBILE TRACKING: GPS tracking status state
  const [gpsStatus, setGpsStatus] = useState('');
  const [locationErrors, setLocationErrors] = useState([]);

  // FIXED: Night mode state with proper tile layer switching
  const [nightMode, setNightMode] = useState(() => {
    // Try to load from localStorage, default to false
    try {
      const saved = window.localStorage?.getItem('vehicleMapNightMode');
      return saved ? JSON.parse(saved) : false;
    } catch {
      return false;
    }
  });

  // State to trigger marker refresh when night mode changes
  const [markerRefreshTrigger, setMarkerRefreshTrigger] = useState(0);

  // Vehicle tracking state
  const [securedVehicles, setSecuredVehicles] = useState(new Map());
  const [newlyAddedVehicles, setNewlyAddedVehicles] = useState(new Map());
  const [previousTeamVehicles, setPreviousTeamVehicles] = useState([]);

  // Order tracking state
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [showOrderPopup, setShowOrderPopup] = useState(false);
  const [previousOrders, setPreviousOrders] = useState([]);
  const [newlyAddedOrders, setNewlyAddedOrders] = useState(new Map());

  // Vehicle editing state
  const [editingMapVehicle, setEditingMapVehicle] = useState(null);
  const [editMapVehicleData, setEditMapVehicleData] = useState({});
  const [savingMapEdit, setSavingMapEdit] = useState(false);

  // Vehicle selection state
  const [vehicleSelectionNotification, setVehicleSelectionNotification] = useState(null);

  // Team member profiles with better loading strategy
  const [teamMemberProfiles, setTeamMemberProfiles] = useState(new Map());
  const [profilesLoading, setProfilesLoading] = useState(false);
  const [profilesLoaded, setProfilesLoaded] = useState(false);

  // NEW: Refresh state
  const [isRefreshing, setIsRefreshing] = useState(false);

  // NEW: Alert integration state
  const [mapContainerClass, setMapContainerClass] = useState('');

  // Check if current user is admin (keep existing admin functionality)
  const isCurrentUserAdmin = useMemo(() => {
    if (!userProfile || !user) return false;
    
    // Check for admin role
    const role = (userProfile.role || '').toLowerCase();
    if (role.includes('admin') || role.includes('manager') || role.includes('supervisor')) {
      return true;
    }
    
    // Check for admin permissions
    if (userProfile.isAdmin === true || userProfile.admin === true) {
      return true;
    }
    
    // Check team ownership or management
    if (team && (team.ownerId === user.id || team.managerId === user.id)) {
      return true;
    }
    
    // Check if user has admin tags
    if (userProfile.tags && Array.isArray(userProfile.tags)) {
      const hasAdminTag = userProfile.tags.some(tag => {
        const tagName = (tag.name || '').toLowerCase();
        return tagName.includes('admin') || tagName.includes('manager') || tagName.includes('supervisor');
      });
      if (hasAdminTag) return true;
    }
    
    return false;
  }, [userProfile, user, team]);

  // FIXED: Night mode toggle function with proper tile layer switching
  const toggleNightMode = useCallback(() => {
    setNightMode(prev => {
      const newMode = !prev;
      // Save to localStorage
      try {
        window.localStorage?.setItem('vehicleMapNightMode', JSON.stringify(newMode));
      } catch (error) {
        console.warn('Could not save night mode preference:', error);
      }
      return newMode;
    });
  }, []);

  // FIXED: Force marker recreation when night mode changes
  const refreshAllMarkers = useCallback(() => {
    if (!mapRef.current) return;

    console.log('🔄 Forcing complete marker recreation for theme change...');

    // Force a small delay to ensure tile layer is loaded, then trigger marker recreation
    setTimeout(() => {
      try {
        // Increment the refresh trigger to force all marker effects to re-run
        setMarkerRefreshTrigger(prev => prev + 1);
        
        // Force map redraw
        mapRef.current.invalidateSize();
        
        console.log('✅ Marker recreation triggered successfully');
      } catch (error) {
        console.error('Error triggering marker refresh:', error);
      }
    }, 150);
  }, []);

  // NEW: Refresh map data function
  const refreshMapData = useCallback(async () => {
    if (isRefreshing) return;
    
    setIsRefreshing(true);
    
    try {
      console.log('🔄 Refreshing map data...');
      
      // Clear all existing markers
      Object.values(vehicleMarkersRef.current).forEach(marker => {
        if (marker && mapRef.current && mapRef.current.hasLayer(marker)) {
          mapRef.current.removeLayer(marker);
        }
      });
      vehicleMarkersRef.current = {};
      
      Object.values(orderMarkersRef.current).forEach(markers => {
        if (Array.isArray(markers)) {
          markers.forEach(marker => {
            if (marker && mapRef.current && mapRef.current.hasLayer(marker)) {
              mapRef.current.removeLayer(marker);
            }
          });
        } else if (markers && mapRef.current && mapRef.current.hasLayer(markers)) {
          mapRef.current.removeLayer(markers);
        }
      });
      orderMarkersRef.current = {};
      
      Object.values(teammateMarkersRef.current).forEach(marker => {
        if (marker && mapRef.current && mapRef.current.hasLayer(marker)) {
          mapRef.current.removeLayer(marker);
        }
      });
      teammateMarkersRef.current = {};
      
      Object.values(trailLayersRef.current).forEach(trail => {
        if (trail && mapRef.current && mapRef.current.hasLayer(trail)) {
          mapRef.current.removeLayer(trail);
        }
      });
      trailLayersRef.current = {};
      
      // Clear geocoding cache and queue
      geocodingCache.current = {};
      geocodingQueue.current = [];
      
      // Force marker recreation
      setMarkerRefreshTrigger(prev => prev + 1);
      
      // Force map invalidation
      if (mapRef.current) {
        mapRef.current.invalidateSize();
      }
      
      console.log('✅ Map data refreshed successfully');
      
      // Show success notification briefly
      setGeocodingStatus('Map refreshed successfully!');
      setTimeout(() => {
        setGeocodingStatus(null);
      }, 2000);
      
    } catch (error) {
      console.error('Error refreshing map data:', error);
      setGeocodingStatus('Error refreshing map data');
      setTimeout(() => {
        setGeocodingStatus(null);
      }, 3000);
    } finally {
      // Add a small delay before allowing refresh again
      setTimeout(() => {
        setIsRefreshing(false);
      }, 1000);
    }
  }, [isRefreshing]);

  // MOBILE TRACKING: Test GPS function
  const testGPS = useCallback(() => {
    console.log('🔍 Testing GPS...');
    setGpsStatus('Testing GPS...');
    
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude, accuracy } = position.coords;
        console.log('✅ GPS TEST SUCCESS:', { latitude, longitude, accuracy });
        setGpsStatus(`✅ GPS OK: ${latitude.toFixed(6)}, ${longitude.toFixed(6)} (±${Math.round(accuracy)}m)`);
        alert(`📍 GPS Test Successful!\n\nLatitude: ${latitude.toFixed(6)}\nLongitude: ${longitude.toFixed(6)}\nAccuracy: ±${Math.round(accuracy)} meters\n\nYour GPS is working properly!`);
        
        // Clear status after 5 seconds
        setTimeout(() => setGpsStatus(''), 5000);
      },
      (error) => {
        console.error('❌ GPS TEST FAILED:', error);
        let errorMessage = '';
        
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = '📍 Permission denied. Enable location access in browser settings!';
            setGpsStatus('❌ Location permission denied');
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = '📍 GPS unavailable. Try moving to an area with better GPS signal.';
            setGpsStatus('❌ GPS signal unavailable');
            break;
          case error.TIMEOUT:
            errorMessage = '📍 GPS timeout. Check your device GPS settings and try again.';
            setGpsStatus('❌ GPS timeout');
            break;
          default:
            errorMessage = `📍 GPS error: ${error.message}`;
            setGpsStatus('❌ GPS error');
            break;
        }
        
        alert(errorMessage);
        
        // Clear status after 5 seconds
        setTimeout(() => setGpsStatus(''), 5000);
      },
      GEOLOCATION_OPTIONS
    );
  }, []);

  // FIXED: Apply night mode with proper tile layer switching and marker refresh
  useEffect(() => {
    if (!mapRef.current || !tileLayerRef.current) return;

    try {
      // Remove current tile layer
      if (tileLayerRef.current) {
        mapRef.current.removeLayer(tileLayerRef.current);
      }

      // Add new tile layer based on night mode
      const config = nightMode ? TILE_LAYER_CONFIGS.night : TILE_LAYER_CONFIGS.day;
      const newTileLayer = L.tileLayer(config.url, {
        attribution: config.attribution,
        maxZoom: config.maxZoom,
        errorTileUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        retry: true
      });

      // Add loading handlers for tile layer switching
      newTileLayer.on('loading', () => {
        console.log(`🔄 Switching to ${nightMode ? 'dark' : 'light'} tiles...`);
      });

      newTileLayer.on('load', () => {
        console.log(`✅ ${nightMode ? 'Dark' : 'Light'} tiles loaded successfully`);
        // Force map refresh after tile switch
        setTimeout(() => {
          if (mapRef.current) {
            mapRef.current.invalidateSize();
          }
        }, 100);
      });

      newTileLayer.addTo(mapRef.current);
      tileLayerRef.current = newTileLayer;

      // Apply CSS classes for additional styling
      if (mapContainerRef.current) {
        const mapContainer = mapContainerRef.current;
        if (nightMode) {
          mapContainer.classList.add('night-mode');
        } else {
          mapContainer.classList.remove('night-mode');
        }
      }
      
      // Apply night mode to leaflet container
      if (mapRef.current) {
        const leafletContainer = mapRef.current.getContainer();
        if (nightMode) {
          leafletContainer.classList.add('night-mode');
        } else {
          leafletContainer.classList.remove('night-mode');
        }
      }

      // FIXED: Refresh all markers after tile layer change
      refreshAllMarkers();

      console.log(`🌙 Night mode ${nightMode ? 'enabled' : 'disabled'} - tile layer switched to ${nightMode ? 'dark' : 'light'} theme`);
    } catch (error) {
      console.error('Error switching tile layers:', error);
    }
  }, [nightMode, mapReady, refreshAllMarkers]);

  // Create order markers (multiple markers per order)
  const createOrderMarkers = useCallback((order) => {
    const markers = [];
    const statusInfo = ORDER_STATUS_MAPPING[order.status] || { class: 'open' };
    const statusClass = `order-${statusInfo.class}`;
    const isSelected = selectedOrderId === order.id;
    const selectedClass = isSelected ? ' order-selected' : '';
    const orderColor = getOrderColor(order.id);
    const vehicleIcon = getVehicleIconForOrder(order);
    const hasMultipleAddresses = order.addresses && order.addresses.length > 1;
    const multiAddressClass = hasMultipleAddresses ? ' order-multi-address' : '';

    // Create markers for each address
    if (order.addresses && Array.isArray(order.addresses)) {
      order.addresses.forEach((address, index) => {
        let lat, lng;
        
        if (address.position && typeof address.position.lat === 'number' && typeof address.position.lng === 'number') {
          lat = address.position.lat;
          lng = address.position.lng;
        } else {
          return;
        }

        if (isNaN(lat) || isNaN(lng) || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
          return;
        }

        // Use individual address color for multiple addresses, order color for single
        const markerColor = hasMultipleAddresses ? getOrderColor(order.id, index) : orderColor;
        const isGeocoded = !order.position && address.position;
        const geocodedClass = isGeocoded ? ' order-geocoded' : '';

        const icon = L.divIcon({
          className: 'order-marker',
          html: `
            <div class="order-marker-icon ${statusClass}${selectedClass}${geocodedClass}${multiAddressClass}" 
                 style="background: ${markerColor}; border-color: ${hasMultipleAddresses ? '#FFD700' : '#FFFFFF'};">
              ${vehicleIcon}
              ${order.priority ? '<div class="order-priority-badge">!</div>' : ''}
              ${hasMultipleAddresses ? `<div class="order-address-badge">${index + 1}</div>` : ''}
            </div>
          `,
          iconSize: [42, 42],
          iconAnchor: [21, 21]
        });

        const marker = L.marker([lat, lng], {
          icon: icon,
          title: `${order.year} ${order.make} ${order.model} - ${statusInfo.label || order.status}${hasMultipleAddresses ? ` (Address ${index + 1})` : ''} (Click for details)`,
          zIndexOffset: 500 // Orders appear above vehicles but below teammates
        });

        // Add click handler
        marker.on('click', () => {
          handleOrderClick(order);
        });

        markers.push(marker);
      });
    } else if (order.position && typeof order.position.lat === 'number' && typeof order.position.lng === 'number') {
      // Single position marker
      const lat = order.position.lat;
      const lng = order.position.lng;

      if (!isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {
        const icon = L.divIcon({
          className: 'order-marker',
          html: `
            <div class="order-marker-icon ${statusClass}${selectedClass}" 
                 style="background: ${orderColor};">
              ${vehicleIcon}
              ${order.priority ? '<div class="order-priority-badge">!</div>' : ''}
            </div>
          `,
          iconSize: [42, 42],
          iconAnchor: [21, 21]
        });

        const marker = L.marker([lat, lng], {
          icon: icon,
          title: `${order.year} ${order.make} ${order.model} - ${statusInfo.label || order.status} (Click for details)`,
          zIndexOffset: 500
        });

        marker.on('click', () => {
          handleOrderClick(order);
        });

        markers.push(marker);
      }
    }

    return markers;
  }, [selectedOrderId]);

  // Memoized driver type detection
  const getDriverType = useCallback((location) => {
    const userId = location.userId;
    
    // Check location data first (fastest)
    if (location.driverType) {
      return location.driverType.toLowerCase();
    }
    
    // Check loaded team member profile (cached)
    if (userId && teamMemberProfiles.has(userId)) {
      const profile = teamMemberProfiles.get(userId);
      
      // Check explicit driver type
      if (profile.driverType) {
        return profile.driverType.toLowerCase();
      }
      
      // Check tags efficiently
      if (profile.tags && Array.isArray(profile.tags)) {
        for (const tag of profile.tags) {
          const tagName = (tag.name || '').toLowerCase();
          
          // Check for tow truck indicators
          if (DRIVER_TYPE_KEYWORDS.tow.some(keyword => tagName.includes(keyword))) {
            return 'tow';
          }
          
          // Check for camera car indicators
          if (DRIVER_TYPE_KEYWORDS.camera.some(keyword => tagName.includes(keyword))) {
            return 'camera';
          }
        }
      }
      
      // Check job title
      const jobTitle = (profile.jobTitle || '').toLowerCase();
      if (DRIVER_TYPE_KEYWORDS.tow.some(keyword => jobTitle.includes(keyword))) {
        return 'tow';
      }
      if (DRIVER_TYPE_KEYWORDS.camera.some(keyword => jobTitle.includes(keyword))) {
        return 'camera';
      }
      
      // Check vehicle field
      const vehicle = (profile.vehicle || '').toLowerCase();
      if (DRIVER_TYPE_KEYWORDS.tow.some(keyword => vehicle.includes(keyword))) {
        return 'tow';
      }
      if (DRIVER_TYPE_KEYWORDS.camera.some(keyword => vehicle.includes(keyword))) {
        return 'camera';
      }
    }
    
    // Check current user profile (fallback)
    if (userProfile && location.userId === user?.id) {
      if (userProfile.driverType) {
        return userProfile.driverType.toLowerCase();
      }
      
      const role = (userProfile.role || '').toLowerCase();
      const jobTitle = (userProfile.jobTitle || '').toLowerCase();
      
      if (DRIVER_TYPE_KEYWORDS.tow.some(keyword => role.includes(keyword) || jobTitle.includes(keyword))) {
        return 'tow';
      }
      if (DRIVER_TYPE_KEYWORDS.camera.some(keyword => role.includes(keyword) || jobTitle.includes(keyword))) {
        return 'camera';
      }
    }
    
    // Username pattern check (last resort)
    const userName = (location.userName || '').toLowerCase();
    if (DRIVER_TYPE_KEYWORDS.tow.some(keyword => userName.includes(keyword))) {
      return 'tow';
    }
    if (DRIVER_TYPE_KEYWORDS.camera.some(keyword => userName.includes(keyword))) {
      return 'camera';
    }
    
    // Default to camera/spot car
    return 'camera';
  }, [teamMemberProfiles, userProfile, user]);

  // Memoized vehicle icon generation
  const getVehicleIcon = useCallback((driverType, color) => {
    if (driverType === 'tow') {
      return `
        <svg width="20" height="20" viewBox="0 0 24 24" fill="white" stroke="white" stroke-width="1.5">
          <rect x="2" y="12" width="8" height="4" rx="1" fill="white"/>
          <rect x="2" y="8" width="4" height="4" rx="1" fill="white"/>
          <path d="M10 12 L16 8 L18 8 L18 10 L12 14 Z" fill="white"/>
          <circle cx="4" cy="17" r="1.5" fill="${color}" stroke="white" stroke-width="1"/>
          <circle cx="8" cy="17" r="1.5" fill="${color}" stroke="white" stroke-width="1"/>
          <circle cx="16" cy="17" r="1.5" fill="${color}" stroke="white" stroke-width="1"/>
          <circle cx="18" cy="8" r="1" fill="white"/>
        </svg>
      `;
    } else {
      return `
        <svg width="18" height="18" viewBox="0 0 24 24" fill="white" stroke="white" stroke-width="1.5">
          <path d="M5 12 L5 16 L19 16 L19 12 L17 8 L7 8 Z" fill="white"/>
          <path d="M7 8 L9 5 L15 5 L17 8" fill="white"/>
          <circle cx="8" cy="16" r="2" fill="${color}" stroke="white" stroke-width="1"/>
          <circle cx="16" cy="16" r="2" fill="${color}" stroke="white" stroke-width="1"/>
          <path d="M8 8 L10 6 L14 6 L16 8" fill="${color}" opacity="0.7"/>
        </svg>
      `;
    }
  }, []);

  // Debounced profile loading
  const loadTeamMemberProfiles = useCallback(async () => {
    if (!db || !team || profilesLoading || profilesLoaded || allTeamMembers?.size === 0) {
      return;
    }

    // Clear any existing timeout
    if (profileLoadingTimeoutRef.current) {
      clearTimeout(profileLoadingTimeoutRef.current);
    }

    // Debounce profile loading
    profileLoadingTimeoutRef.current = setTimeout(async () => {
      try {
        setProfilesLoading(true);
        
        const profiles = new Map();
        const memberIds = Array.from(allTeamMembers);
        
        // Load profiles in smaller batches
        const batchSize = 5;
        for (let i = 0; i < memberIds.length; i += batchSize) {
          const batch = memberIds.slice(i, i + batchSize);
          
          const profilePromises = batch.map(async (userId) => {
            try {
              const profileRef = doc(db, 'userProfiles', userId);
              const profileDoc = await getDoc(profileRef);
              
              if (profileDoc.exists()) {
                const profileData = profileDoc.data();
                profiles.set(userId, profileData);
              }
            } catch (error) {
              console.error(`❌ Error loading profile for ${userId}:`, error);
            }
          });
          
          await Promise.all(profilePromises);
          
          // Small delay between batches
          if (i + batchSize < memberIds.length) {
            await new Promise(resolve => setTimeout(resolve, 200));
          }
        }
        
        setTeamMemberProfiles(profiles);
        setProfilesLoaded(true);
        
      } catch (error) {
        console.error('❌ Error loading team member profiles:', error);
      } finally {
        setProfilesLoading(false);
      }
    }, 1000);
  }, [db, team, allTeamMembers, profilesLoading, profilesLoaded]);

  // Color assignment with memoization
  const assignTeammateColor = useCallback((userId) => {
    if (teammateColors?.has(userId)) {
      return teammateColors.get(userId);
    }
    
    const usedColors = Array.from(teammateColors?.values() || []);
    const availableColors = TEAM_MEMBER_COLORS.filter(color => !usedColors.includes(color));
    const color = availableColors.length > 0 ? availableColors[0] : TEAM_MEMBER_COLORS[(teammateColors?.size || 0) % TEAM_MEMBER_COLORS.length];
    
    return color;
  }, [teammateColors]);

  // MOBILE TRACKING: Enhanced store location function with better logging
  const storeLocation = useCallback(async (location) => {
    if (!db || !user || !location || !team) return;

    try {
      console.log('📍 STORING LOCATION:', {
        userId: user.id,
        lat: location.lat,
        lng: location.lng,
        accuracy: location.accuracy,
        timestamp: new Date().toISOString()
      });

      const locationData = {
        userId: user.id,
        userName: user.displayName || user.email?.split('@')[0] || 'Unknown',
        teamId: team.id,
        lat: location.lat,
        lng: location.lng,
        accuracy: location.accuracy || null,
        timestamp: serverTimestamp(),
        createdAt: new Date()
      };

      await addDoc(collection(db, 'teamLocations'), locationData);
      
      console.log('✅ Location stored successfully');
      
    } catch (error) {
      console.error('❌ Error storing location:', error);
      setLocationErrors(prev => [...prev.slice(-4), `${new Date().toLocaleTimeString()}: ${error.message}`]);
    }
  }, [db, user, team]);

  // Create teammate marker with driver type icons
  const createTeammateMarker = useCallback((location) => {
    const color = assignTeammateColor(location.userId);
    const isCurrentUser = location.userId === user?.id;
    const displayName = isCurrentUser ? 'You' : (location.userName || 'Teammate');
    const driverType = getDriverType(location);
    
    const isRecent = location.timestamp && (Date.now() - location.timestamp.getTime()) < 10 * 60 * 1000;
    const movingClass = isRecent ? ' moving' : '';

    // Get the appropriate vehicle icon
    const vehicleIcon = getVehicleIcon(driverType, color);

    const icon = L.divIcon({
      className: 'teammate-marker',
      html: `
        <div class="teammate-marker-icon${movingClass}" style="background-color: ${color};">
          ${vehicleIcon}
        </div>
      `,
      iconSize: [32, 32],
      iconAnchor: [16, 16]
    });

    const marker = L.marker([location.lat, location.lng], {
      icon: icon,
      title: `${displayName} (${driverType === 'tow' ? 'Tow Truck' : 'Camera Car'})`,
      zIndexOffset: isCurrentUser ? 2000 : 1500
    });

    const age = location.timestamp ? Math.round((Date.now() - location.timestamp.getTime()) / (1000 * 60)) : 0;
    const ageText = age < 1 ? 'Just now' : age < 60 ? `${age}m ago` : `${Math.round(age/60)}h ago`;

    const driverTypeIcon = driverType === 'tow' ? '🚛' : '🚗';
    const driverTypeText = driverType === 'tow' ? 'Tow Truck Driver' : 'Camera Car Driver';

    // Show profile information in popup
    const profile = teamMemberProfiles.get(location.userId);
    const profileInfo = profile ? `
      <div style="font-size: 10px; color: #888; margin-top: 3px; padding-top: 3px; border-top: 1px solid #eee;">
        ${profile.jobTitle ? `Job: ${profile.jobTitle}<br>` : ''}
        ${profile.vehicle ? `Vehicle: ${profile.vehicle}<br>` : ''}
        ${profile.tags && profile.tags.length > 0 ? `Tags: ${profile.tags.map(t => t.name).join(', ')}` : ''}
      </div>
    ` : '';

    marker.bindPopup(`
      <div style="text-align: center; min-width: 160px;">
        <div style="font-weight: bold; color: ${color}; margin-bottom: 5px; font-size: 14px;">${displayName}</div>
        <div style="font-size: 12px; color: #333; margin-bottom: 5px; padding: 2px 6px; background-color: ${color}; color: white; border-radius: 10px; display: inline-block;">
          ${driverTypeIcon} ${driverTypeText}
        </div>
        <div style="font-size: 11px; color: #666; margin-bottom: 3px;">📍 ${location.lat.toFixed(6)}, ${location.lng.toFixed(6)}</div>
        <div style="font-size: 11px; color: #666; margin-bottom: 3px;">🕒 ${ageText}</div>
        ${location.accuracy ? `<div style="font-size: 11px; color: #666;">📶 ±${Math.round(location.accuracy)}m</div>` : ''}
        ${isCurrentUser ? '<div style="font-size: 10px; color: #3B82F6; margin-top: 5px;">🔵 Your Location</div>' : ''}
        ${profileInfo}
      </div>
    `, {
      closeButton: true,
      autoClose: false,
      keepInView: true,
      maxWidth: 300,
      className: 'teammate-popup'
    });

    return marker;
  }, [assignTeammateColor, user, getDriverType, getVehicleIcon, teamMemberProfiles]);

  // FIXED: Create trail polyline with straight line artifact prevention
  const createTrailPolyline = useCallback((userId, history) => {
    if (!history || history.length < 2) return null;

    const color = assignTeammateColor(userId);
    const isCurrentUser = userId === user?.id;
    
    const cutoffTime = Date.now() - getTimeRangeMs(trailTimeRange);
    const filteredHistory = history.filter(loc => loc.timestamp.getTime() >= cutoffTime);
    
    if (filteredHistory.length < 2) return null;

    // FIXED: Filter out points that would create unrealistic straight lines
    const validTrailSegments = [];
    let currentSegment = [filteredHistory[0]];

    for (let i = 1; i < filteredHistory.length; i++) {
      const prevPoint = filteredHistory[i - 1];
      const currentPoint = filteredHistory[i];
      
      if (shouldConnectTrailPoints(prevPoint, currentPoint)) {
        currentSegment.push(currentPoint);
      } else {
        // End current segment and start a new one
        if (currentSegment.length >= 2) {
          validTrailSegments.push([...currentSegment]);
        }
        currentSegment = [currentPoint];
        console.log(`🔄 Starting new trail segment for ${userId === user?.id ? 'You' : 'teammate'} due to distance/speed constraints`);
      }
    }
    
    // Add the final segment if it has enough points
    if (currentSegment.length >= 2) {
      validTrailSegments.push(currentSegment);
    }
    
    // Create polylines for each valid segment
    if (validTrailSegments.length === 0) return null;
    
    const polylines = validTrailSegments.map(segment => {
      const points = segment.map(loc => [loc.lat, loc.lng]);
      
      return L.polyline(points, {
        color: color,
        weight: isCurrentUser ? 5 : 3,
        opacity: isCurrentUser ? 0.9 : 0.7,
        dashArray: isCurrentUser ? null : '8, 12',
        lineCap: 'round',
        lineJoin: 'round'
      });
    });
    
    // If we have multiple segments, create a layer group
    if (polylines.length === 1) {
      const polyline = polylines[0];
      const segment = validTrailSegments[0];
      const userName = isCurrentUser ? 'Your' : (segment[0]?.userName || 'Teammate\'s');
      const duration = Math.round((segment[segment.length - 1].timestamp.getTime() - segment[0].timestamp.getTime()) / (1000 * 60));
      const distance = calculateTrailDistance(segment);
      
      polyline.bindTooltip(`${userName} trail: ${duration}m duration, ${segment.length} points, ~${distance}m distance`, {
        permanent: false,
        direction: 'top',
        className: 'trail-tooltip'
      });
      
      return polyline;
    } else {
      // Create a layer group for multiple segments
      const layerGroup = L.layerGroup(polylines);
      const totalPoints = validTrailSegments.reduce((sum, seg) => sum + seg.length, 0);
      const userName = isCurrentUser ? 'Your' : (filteredHistory[0]?.userName || 'Teammate\'s');
      
      // Add tooltip to the first polyline
      if (polylines[0]) {
        polylines[0].bindTooltip(`${userName} trail: ${validTrailSegments.length} segments, ${totalPoints} points (filtered for accuracy)`, {
          permanent: false,
          direction: 'top',
          className: 'trail-tooltip'
        });
      }
      
      return layerGroup;
    }
  }, [assignTeammateColor, user, trailTimeRange]);

  // Calculate approximate trail distance
  const calculateTrailDistance = (locations) => {
    if (locations.length < 2) return 0;
    
    let totalDistance = 0;
    for (let i = 1; i < locations.length; i++) {
      const prev = locations[i - 1];
      const curr = locations[i];
      
      const distance = calculateDistance(prev.lat, prev.lng, curr.lat, curr.lng);
      totalDistance += distance;
    }
    
    return Math.round(totalDistance);
  };

  // Handle order click
  const handleOrderClick = useCallback((order) => {
    setSelectedOrder(order);
    setShowOrderPopup(true);
    
    // Notification
    setVehicleSelectionNotification({
      vehicle: `${order.year} ${order.make} ${order.model}`,
      vin: order.vin,
      type: 'order'
    });
    
    setTimeout(() => {
      setVehicleSelectionNotification(null);
    }, 4000);
    
    // Call parent callback
    if (onOrderSelect && typeof onOrderSelect === 'function') {
      onOrderSelect(order.id);
    }
  }, [onOrderSelect]);

  // Handle order navigation
  const handleOrderNavigate = useCallback((order) => {
    if (onOrderNavigate && typeof onOrderNavigate === 'function') {
      onOrderNavigate(order);
    } else {
      // Default navigation behavior - navigate to first address with position
      let targetPosition = order.position;
      
      if (!targetPosition && order.addresses && order.addresses.length > 0) {
        const addressWithPosition = order.addresses.find(addr => 
          addr.position && addr.position.lat && addr.position.lng
        );
        if (addressWithPosition) {
          targetPosition = addressWithPosition.position;
        }
      }
      
      if (targetPosition && mapRef.current) {
        mapRef.current.setView([targetPosition.lat, targetPosition.lng], 16);
      }
    }
  }, [onOrderNavigate]);

  // Handle order secure
  const handleOrderSecure = useCallback((order) => {
    if (onOrderSecure && typeof onOrderSecure === 'function') {
      onOrderSecure(order);
    }
  }, [onOrderSecure]);

  // Enhanced geocode with strict IL/IN/WI bounds
  const geocodeAddress = async (address, city, state, zipCode) => {
    let fullAddress = address;
    if (city || state || zipCode) {
      fullAddress = buildFullAddress(address, city, state, zipCode);
    }
    
    if (!fullAddress || typeof fullAddress !== 'string') return null;
    
    const normalizedAddress = fullAddress.trim();
    
    if (geocodingCache.current[normalizedAddress]) {
      return geocodingCache.current[normalizedAddress];
    }
    
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setGeocodingStatus(`Geocoding: ${normalizedAddress.substring(0, 30)}...`);
      
      const strictBounds = {
        minLat: 37.0,
        maxLat: 47.0,
        minLng: -92.5,
        maxLng: -84.5
      };
      
      const isWithinBounds = (lat, lng) => {
        return lat >= strictBounds.minLat && 
               lat <= strictBounds.maxLat && 
               lng >= strictBounds.minLng && 
               lng <= strictBounds.maxLng;
      };
      
      let position = null;
      
      if (city && state) {
        const validStates = ['IL', 'IN', 'WI', 'Illinois', 'Indiana', 'Wisconsin'];
        const stateUpper = state.toUpperCase();
        const isValidState = validStates.some(s => s.toUpperCase() === stateUpper || s.toUpperCase().startsWith(stateUpper));
        
        if (!isValidState) {
          console.warn(`Invalid state "${state}" - must be IL, IN, or WI`);
          return null;
        }
        
        const structuredQuery = new URLSearchParams({
          format: 'json',
          street: address || '',
          city: city,
          state: state,
          postalcode: zipCode || '',
          countrycodes: 'us',
          bounded: '1',
          viewbox: `${strictBounds.minLng},${strictBounds.maxLat},${strictBounds.maxLng},${strictBounds.minLat}`,
          limit: '1'
        });
        
        const responseStructured = await fetch(
          `https://nominatim.openstreetmap.org/search?${structuredQuery}`,
          {
            headers: {
              'User-Agent': 'VehicleMapDisplay/1.0'
            }
          }
        );
        
        if (responseStructured.ok) {
          const dataStructured = await responseStructured.json();
          if (dataStructured && dataStructured.length > 0) {
            const lat = parseFloat(dataStructured[0].lat);
            const lng = parseFloat(dataStructured[0].lon);
            
            if (isWithinBounds(lat, lng)) {
              position = { lat, lng };
            }
          }
        }
      }
      
      if (!position) {
        const responseWithBounds = await fetch(
          `https://nominatim.openstreetmap.org/search?` +
          `format=json&` +
          `q=${encodeURIComponent(normalizedAddress)}&` +
          `countrycodes=us&` +
          `bounded=1&` +
          `viewbox=${strictBounds.minLng},${strictBounds.maxLat},${strictBounds.maxLng},${strictBounds.minLat}&` +
          `limit=1`,
          {
            headers: {
              'User-Agent': 'VehicleMapDisplay/1.0'
            }
          }
        );
        
        if (responseWithBounds.ok) {
          const dataWithBounds = await responseWithBounds.json();
          if (dataWithBounds && dataWithBounds.length > 0) {
            const lat = parseFloat(dataWithBounds[0].lat);
            const lng = parseFloat(dataWithBounds[0].lon);
            
            if (isWithinBounds(lat, lng)) {
              position = { lat, lng };
            }
          }
        }
      }
      
      if (position) {
        if (!isWithinBounds(position.lat, position.lng)) {
          console.error(`Final validation failed - coordinates outside IL/IN/WI: ${position.lat}, ${position.lng}`);
          return null;
        }
        
        geocodingCache.current[normalizedAddress] = position;
        return position;
      } else {
        return null;
      }
    } catch (error) {
      console.error(`Error geocoding address "${normalizedAddress}":`, error);
      return null;
    } finally {
      setGeocodingStatus(null);
    }
  };

  // Edit vehicle from map
  const handleEditMapVehicle = (vehicle) => {
    setEditingMapVehicle(vehicle);
    setEditMapVehicleData({
      address: vehicle.address || '',
      city: vehicle.city || '',
      state: vehicle.state || '',
      zipCode: vehicle.zipCode || '',
      fullAddress: vehicle.fullAddress || ''
    });
  };

  // Save map vehicle edits and re-geocode
  const handleSaveMapVehicle = async () => {
    if (!editingMapVehicle || !db || !user) return;

    try {
      setSavingMapEdit(true);

      let cleanAddress = editMapVehicleData.address;
      if (editMapVehicleData.city && editMapVehicleData.state) {
        const cityRegex = new RegExp(`,?\\s*${editMapVehicleData.city.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\s*,?`, 'gi');
        const stateRegex = new RegExp(`,?\\s*${editMapVehicleData.state.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\s*,?`, 'gi');
        cleanAddress = cleanAddress.replace(cityRegex, '').replace(stateRegex, '').replace(/,+/g, ',').replace(/^,|,$/g, '').trim();
      }

      const position = await geocodeAddress(
        cleanAddress,
        editMapVehicleData.city,
        editMapVehicleData.state,
        editMapVehicleData.zipCode
      );

      const updateData = {
        address: cleanAddress,
        city: editMapVehicleData.city,
        state: editMapVehicleData.state,
        zipCode: editMapVehicleData.zipCode,
        fullAddress: buildFullAddress(
          cleanAddress,
          editMapVehicleData.city,
          editMapVehicleData.state,
          editMapVehicleData.zipCode
        ),
        updatedAt: new Date()
      };

      if (position) {
        updateData.position = position;
      }

      const docRef = doc(db, 'users', editingMapVehicle.teamMemberId, 'vehicleWeeks', editingMapVehicle.weekId, 'vehicles', editingMapVehicle.id);
      await updateDoc(docRef, updateData);

      setEditingMapVehicle(null);
      setEditMapVehicleData({});

      alert(`Successfully updated address for ${editingMapVehicle.vehicle}${position ? ' and re-geocoded location!' : '!'}`);

    } catch (error) {
      console.error('Error updating vehicle address:', error);
      alert('Error updating vehicle address. Please try again.');
    } finally {
      setSavingMapEdit(false);
    }
  };

  // Process geocoding queue
  const processGeocodingQueue = useCallback(async () => {
    if (geocodingQueue.current.length === 0) return;

    const vehicle = geocodingQueue.current.shift();
    if (!vehicle) return;

    const position = await geocodeAddress(
      vehicle.address,
      vehicle.city,
      vehicle.state,
      vehicle.zipCode
    );

    if (position && mapRef.current) {
      const uniqueKey = vehicle.uniqueKey || vehicle.id;

      if (vehicleMarkersRef.current[uniqueKey]) {
        mapRef.current.removeLayer(vehicleMarkersRef.current[uniqueKey]);
      }

      const vehicleWithPosition = { ...vehicle, position };
      const marker = createVehicleMarker(vehicleWithPosition);
      if (marker) {
        marker.addTo(mapRef.current);
        vehicleMarkersRef.current[uniqueKey] = marker;
      }
    }

    if (geocodingQueue.current.length > 0) {
      setTimeout(() => processGeocodingQueue(), 1100);
    }
  }, []);

  // Vehicle selection handler
  const handleVehicleClick = useCallback((vehicle) => {
    // Simple notification without driver type info for team vehicles
    setVehicleSelectionNotification({
      vehicle: vehicle.vehicle,
      vin: vehicle.vin
    });

    // Auto-hide notification
    setTimeout(() => {
      setVehicleSelectionNotification(null);
    }, 4000);

    // Call parent callback
    if (onVehicleSelect && typeof onVehicleSelect === 'function') {
      onVehicleSelect(vehicle.uniqueKey);
    }
  }, [onVehicleSelect]);

  // Create vehicle marker with click handler
  const createVehicleMarker = useCallback((vehicle) => {
    let lat, lng;

    if (vehicle.position && typeof vehicle.position.lat === 'number' && typeof vehicle.position.lng === 'number') {
      lat = vehicle.position.lat;
      lng = vehicle.position.lng;
    } else if (typeof vehicle.lat === 'number' && typeof vehicle.lng === 'number') {
      lat = vehicle.lat;
      lng = vehicle.lng;
    } else {
      if ((vehicle.address || vehicle.city || vehicle.fullAddress) && !geocodingQueue.current.some(v => v.uniqueKey === vehicle.uniqueKey)) {
        geocodingQueue.current.push(vehicle);
      }
      return null;
    }

    if (isNaN(lat) || isNaN(lng) || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
      console.error(`Invalid coordinates for vehicle ${vehicle.vehicle}: ${lat}, ${lng}`);
      return null;
    }

    // Determine background color based on status
    let backgroundColor, textColor;

    if (vehicle.bottomStatus) {
      backgroundColor = '#DC2626';
      textColor = 'white';
    } else {
      switch (vehicle.status) {
        case 'SECURED':
          backgroundColor = '#10B981';
          textColor = 'white';
          break;
        case 'FOUND':
          backgroundColor = '#F59E0B';
          textColor = 'white';
          break;
        case 'PENDING PICKUP':
          backgroundColor = '#3B82F6';
          textColor = 'white';
          break;
        case 'DO NOT SECURE':
          backgroundColor = '#8B5CF6';
          textColor = 'white';
          break;
        case 'NOT FOUND':
          backgroundColor = '#EF4444';
          textColor = 'white';
          break;
        default:
          backgroundColor = '#6B7280';
          textColor = 'white';
      }
    }

    const hasOriginalGPS = vehicle.position && vehicle.position.lat && vehicle.position.lng;
    const borderStyle = hasOriginalGPS ? 'solid' : 'dashed';
    const borderWidth = hasOriginalGPS ? '3px' : '2px';

    let animationClasses = '';
    const now = Date.now();

    if (vehicle.status === 'SECURED' && securedVehicles.has(vehicle.uniqueKey)) {
      animationClasses += ' secured-fading';
    }

    if (newlyAddedVehicles.has(vehicle.uniqueKey)) {
      const addedTime = newlyAddedVehicles.get(vehicle.uniqueKey);
      const oneHour = 60 * 60 * 1000;
      if (now - addedTime < oneHour) {
        animationClasses += ' newly-added';
      }
    }

    // Check if this vehicle is selected
    const isSelected = selectedVehicleId === vehicle.uniqueKey;
    if (isSelected) {
      animationClasses += ' selected';
    }

    // Enhanced car icon
    const carIcon = `
      <svg width="18" height="18" viewBox="0 0 24 24" fill="${textColor}" stroke="${textColor}" stroke-width="1">
        <path d="M18.92 5.01C18.72 4.42 18.16 4 17.5 4h-11c-.66 0-1.21.42-1.42 1.01L3 11v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 15c-.83 0-1.5-.67-1.5-1.5S5.67 12 6.5 12s1.5.67 1.5 1.5S7.33 15 6.5 15zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 10l1.5-4.5h11L19 10H5z"/>
      </svg>
    `;

    const icon = L.divIcon({
      className: 'vehicle-marker',
      html: `
        <div style="
          width: 32px;
          height: 32px;
          background-color: ${backgroundColor};
          border: ${borderWidth} solid white;
          border-style: ${borderStyle};
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 2px 6px rgba(0,0,0,0.4), 0 0 0 1px rgba(0,0,0,0.1);
          transition: transform 0.2s ease, opacity 0.3s ease;
          position: relative;
          cursor: pointer;
        " class="vehicle-marker-icon-custom${animationClasses}">
          ${carIcon}
          ${vehicle.bottomStatus ? '<div style="position: absolute; top: -2px; right: -2px; width: 8px; height: 8px; background: #DC2626; border: 1px solid white; border-radius: 50%;"></div>' : ''}
        </div>
      `,
      iconSize: [36, 36],
      iconAnchor: [18, 18]
    });

    const marker = L.marker([lat, lng], {
      icon: icon,
      title: `${vehicle.vehicle || 'Vehicle'} (Click to select in tracker below)`,
      zIndexOffset: 1000 // Vehicles appear above orders
    });

    // Add click handler
    marker.on('click', () => {
      handleVehicleClick(vehicle);
    });

    return marker;
  }, [securedVehicles, newlyAddedVehicles, selectedVehicleId, handleVehicleClick]);

  // MOBILE TRACKING: Enhanced update current location marker with better error handling
  const updateCurrentLocationMarker = useCallback((location) => {
    if (!mapRef.current || !location) return;

    try {
      console.log('📍 UPDATING LOCATION MARKER:', {
        lat: location.lat,
        lng: location.lng,
        accuracy: location.accuracy,
        timestamp: new Date().toISOString()
      });

      if (currentLocationMarkerRef.current) {
        mapRef.current.removeLayer(currentLocationMarkerRef.current);
      }

      const userIcon = L.divIcon({
        className: 'current-location-marker',
        html: '<div class="current-marker-inner"></div>',
        iconSize: [24, 24],
        iconAnchor: [12, 12]
      });

      const marker = L.marker([location.lat, location.lng], {
        icon: userIcon,
        zIndexOffset: 3000, // Current location appears above everything
        title: "Your Location"
      }).addTo(mapRef.current);

      marker.bindPopup(`
        <div style="text-align: center;">
          <strong>Your Location</strong><br>
          <span style="font-size: 12px;">${location.lat.toFixed(6)}, ${location.lng.toFixed(6)}</span>
          <br><span style="font-size: 10px; color: #666;">Accuracy: ±${location.accuracy ? Math.round(location.accuracy) : '?'}m</span>
        </div>
      `);

      currentLocationMarkerRef.current = marker;

      // Store location for teammate tracking
      storeLocation(location);

    } catch (error) {
      console.error('Error updating location marker:', error);
      setLocationErrors(prev => [...prev.slice(-4), `${new Date().toLocaleTimeString()}: ${error.message}`]);
    }
  }, [storeLocation]);

  // MOBILE TRACKING: Enhanced start location tracking with better mobile support
  const startLocationTracking = useCallback(() => {
    if (locationWatchIdRef.current) return;

    console.log('🎯 STARTING MOBILE LOCATION TRACKING...');
    setGpsStatus('Starting GPS tracking...');

    if ('geolocation' in navigator) {
      locationWatchIdRef.current = navigator.geolocation.watchPosition(
        (position) => {
          const { latitude, longitude, accuracy } = position.coords;
          console.log('📍 NEW MOBILE POSITION:', {
            latitude,
            longitude,
            accuracy,
            timestamp: new Date().toISOString()
          });

          const newLocation = {
            lat: latitude,
            lng: longitude,
            accuracy: accuracy,
            timestamp: Date.now()
          };
          
          setCurrentLocation(newLocation);
          updateCurrentLocationMarker(newLocation);
          setGpsStatus(`✅ GPS Active: ±${Math.round(accuracy)}m accuracy`);
          
          // Clear any location errors
          setLocationErrors([]);
        },
        (error) => {
          console.error('❌ MOBILE GEOLOCATION ERROR:', error);
          let errorMessage = '';
          
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = '📍 Enable location access in browser settings!';
              setGpsStatus('❌ Location permission denied');
              alert('📍 Location access denied!\n\nTo enable GPS tracking:\n1. Click the location icon in your browser\'s address bar\n2. Select "Allow" for location access\n3. Refresh the page');
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = '📍 GPS unavailable. Try moving to an area with better GPS signal.';
              setGpsStatus('❌ GPS signal unavailable');
              break;
            case error.TIMEOUT:
              errorMessage = '📍 GPS timeout. Check your device GPS settings and try again.';
              setGpsStatus('❌ GPS timeout');
              break;
            default:
              errorMessage = `📍 GPS error: ${error.message}`;
              setGpsStatus('❌ GPS error');
              break;
          }
          
          setLocationErrors(prev => [...prev.slice(-4), `${new Date().toLocaleTimeString()}: ${errorMessage}`]);
          console.error('Geolocation error details:', errorMessage);
        },
        GEOLOCATION_OPTIONS
      );

      console.log('✅ GPS watch started with ID:', locationWatchIdRef.current);
    } else {
      const errorMsg = '❌ Geolocation not supported by this browser';
      console.error(errorMsg);
      setGpsStatus(errorMsg);
      setLocationErrors(prev => [...prev.slice(-4), `${new Date().toLocaleTimeString()}: ${errorMsg}`]);
    }
  }, [updateCurrentLocationMarker]);

  // Stop location tracking
  const stopLocationTracking = useCallback(() => {
    if (locationWatchIdRef.current) {
      console.log('🛑 STOPPING GPS TRACKING...');
      navigator.geolocation.clearWatch(locationWatchIdRef.current);
      locationWatchIdRef.current = null;
      setGpsStatus('GPS tracking stopped');
      setTimeout(() => setGpsStatus(''), 3000);
    }
  }, []);

  // Find my location
  const findMyLocation = useCallback(() => {
    if (currentLocation && mapRef.current) {
      mapRef.current.setView([currentLocation.lat, currentLocation.lng], 15);
    } else {
      console.log('🔍 Getting current position...');
      setGpsStatus('Getting your location...');

      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude, accuracy } = position.coords;
          console.log('✅ Got current position:', { latitude, longitude, accuracy });
          
          const location = { 
            lat: latitude, 
            lng: longitude, 
            accuracy: accuracy 
          };
          
          setCurrentLocation(location);
          updateCurrentLocationMarker(location);
          setGpsStatus(`✅ Location found: ±${Math.round(accuracy)}m`);
          
          if (mapRef.current) {
            mapRef.current.setView([latitude, longitude], 15);
          }
          
          setTimeout(() => setGpsStatus(''), 3000);
        },
        (error) => {
          console.error('❌ Error getting location:', error);
          let errorMessage = '';
          
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = '📍 Enable location access in browser settings!';
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = '📍 GPS unavailable. Try moving to an area with better GPS signal.';
              break;
            case error.TIMEOUT:
              errorMessage = '📍 GPS timeout. Check your device GPS settings.';
              break;
            default:
              errorMessage = `📍 GPS error: ${error.message}`;
              break;
          }
          
          setGpsStatus(errorMessage);
          setTimeout(() => setGpsStatus(''), 5000);
        },
        GEOLOCATION_OPTIONS
      );
    }
  }, [currentLocation, updateCurrentLocationMarker]);

  // Add styles to document
  useEffect(() => {
    if (!document.getElementById('vehicle-map-styles')) {
      const styleEl = document.createElement('style');
      styleEl.id = 'vehicle-map-styles';
      styleEl.textContent = mapStyles;
      document.head.appendChild(styleEl);
    }
  }, []);

  // Detect newly added orders
  useEffect(() => {
    if (previousOrders.length === 0) {
      setPreviousOrders(orders);
      return;
    }

    const previousIds = new Set(previousOrders.map(o => o.id));
    const newOrders = orders.filter(o => 
      !previousIds.has(o.id) && 
      shouldShowOrderOnMap(o, team?.id)
    );

    if (newOrders.length > 0) {
      playMoneySound();

      const now = Date.now();
      setNewlyAddedOrders(prev => {
        const updated = new Map(prev);
        newOrders.forEach(order => {
          updated.set(order.id, now);
        });
        return updated;
      });
    }

    setPreviousOrders(orders);
  }, [orders, team?.id]);

  // Detect newly added vehicles and play money sound
  useEffect(() => {
    if (previousTeamVehicles.length === 0) {
      setPreviousTeamVehicles(teamVehicles);
      return;
    }

    const previousIds = new Set(previousTeamVehicles.map(v => v.uniqueKey));
    const newVehicles = teamVehicles.filter(v => 
      !previousIds.has(v.uniqueKey) && 
      (v.status === 'PENDING PICKUP' || v.status === 'FOUND')
    );

    if (newVehicles.length > 0) {
      playMoneySound();

      const now = Date.now();
      setNewlyAddedVehicles(prev => {
        const updated = new Map(prev);
        newVehicles.forEach(vehicle => {
          updated.set(vehicle.uniqueKey, now);
        });
        return updated;
      });
    }

    setPreviousTeamVehicles(teamVehicles);
  }, [teamVehicles]);

  // Track when vehicles change to SECURED status
  useEffect(() => {
    const now = Date.now();

    teamVehicles.forEach(vehicle => {
      if (vehicle.status === 'SECURED' && !securedVehicles.has(vehicle.uniqueKey)) {
        setSecuredVehicles(prev => {
          const updated = new Map(prev);
          updated.set(vehicle.uniqueKey, now);
          return updated;
        });
      }
    });
  }, [teamVehicles, securedVehicles]);

  // Cleanup timers for secured vehicles (10 minutes) and newly added vehicles (1 hour)
  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now();
      const tenMinutes = 10 * 60 * 1000;
      const oneHour = 60 * 60 * 1000;

      setSecuredVehicles(prev => {
        const updated = new Map();
        prev.forEach((timestamp, vehicleKey) => {
          if (now - timestamp < tenMinutes) {
            updated.set(vehicleKey, timestamp);
          } else {
            if (vehicleMarkersRef.current[vehicleKey]) {
              mapRef.current?.removeLayer(vehicleMarkersRef.current[vehicleKey]);
              delete vehicleMarkersRef.current[vehicleKey];
            }
          }
        });
        return updated;
      });
      setNewlyAddedVehicles(prev => {
        const updated = new Map();
        prev.forEach((timestamp, vehicleKey) => {
          if (now - timestamp < oneHour) {
            updated.set(vehicleKey, timestamp);
          }
        });
        return updated;
      });

// Cleanup order animations
setNewlyAddedOrders(prev => {
  const updated = new Map();
  prev.forEach((timestamp, orderId) => {
    if (now - timestamp < oneHour) {
      updated.set(orderId, timestamp);
    }
  });
  return updated;
});
}, 30000);

return () => clearInterval(interval);
}, []);

// FIXED: Map initialization with proper tile layer setup and error handling
useEffect(() => {
if (!mapContainerRef.current || mapRef.current) return;

setMapLoading(true);
setMapError(null);

try {
const map = L.map(mapContainerRef.current, {
  zoomControl: true,
  dragging: true,
  touchZoom: true,
  scrollWheelZoom: true,
  doubleClickZoom: true,
  boxZoom: true,
  keyboard: true,
}).setView([mapCenter.lat, mapCenter.lng], 8);

// FIXED: Use proper tile layer configuration based on night mode with error handling
const config = nightMode ? TILE_LAYER_CONFIGS.night : TILE_LAYER_CONFIGS.day;
const tileLayer = L.tileLayer(config.url, {
  attribution: config.attribution,
  maxZoom: config.maxZoom,
  errorTileUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', // 1x1 transparent PNG
  retry: true
});

// Add loading and error event handlers
tileLayer.on('loading', () => {
  console.log('🔄 Tile layer loading...');
});

tileLayer.on('load', () => {
  console.log('✅ Tile layer loaded successfully');
  setMapLoading(false);
  // Ensure map is ready after tiles load
  if (!mapRef.current) return;
  setTimeout(() => {
    if (mapRef.current) {
      mapRef.current.invalidateSize();
      setMapReady(true);
    }
  }, 100);
});

tileLayer.on('tileerror', (error) => {
  console.warn('⚠️ Tile loading error:', error);
  setMapError('Failed to load map tiles. Please check your internet connection.');
  // Don't fail completely on tile errors - still set map as ready
  setMapLoading(false);
  setMapReady(true);
});

tileLayer.addTo(map);
tileLayerRef.current = tileLayer;

mapRef.current = map;

// Set map ready immediately, but also after tile load for safety
setMapReady(true);
setMapLoading(false);

// Force initial marker refresh after a short delay to ensure everything is loaded
setTimeout(() => {
  if (mapRef.current) {
    console.log('🔄 Forcing initial marker refresh...');
    setMarkerRefreshTrigger(prev => prev + 1);
  }
}, 500);

console.log(`🗺️ Map initialized with ${nightMode ? 'dark' : 'light'} theme`);

return () => {
  if (map) {
    Object.values(vehicleMarkersRef.current).forEach(marker => {
      try { 
        if (map.hasLayer(marker)) {
          map.removeLayer(marker); 
        }
      } catch (e) {
        console.warn('Error removing vehicle marker:', e);
      }
    });
    
    Object.values(orderMarkersRef.current).forEach(markers => {
      if (Array.isArray(markers)) {
        markers.forEach(marker => {
          try { 
            if (map.hasLayer(marker)) {
              map.removeLayer(marker); 
            }
          } catch (e) {
            console.warn('Error removing order marker:', e);
          }
        });
      } else if (markers) {
        try { 
          if (map.hasLayer(markers)) {
            map.removeLayer(markers); 
          }
        } catch (e) {
          console.warn('Error removing order marker:', e);
        }
      }
    });
    
    Object.values(teammateMarkersRef.current).forEach(marker => {
      try { 
        if (map.hasLayer(marker)) {
          map.removeLayer(marker); 
        }
      } catch (e) {
        console.warn('Error removing teammate marker:', e);
      }
    });
    
    Object.values(trailLayersRef.current).forEach(trail => {
      try { 
        if (map.hasLayer(trail)) {
          map.removeLayer(trail); 
        }
      } catch (e) {
        console.warn('Error removing trail:', e);
      }
    });

    if (currentLocationMarkerRef.current && map.hasLayer(currentLocationMarkerRef.current)) {
      try {
        map.removeLayer(currentLocationMarkerRef.current);
      } catch (e) {
        console.warn('Error removing current location marker:', e);
      }
    }

    if (locationWatchIdRef.current) {
      navigator.geolocation.clearWatch(locationWatchIdRef.current);
      locationWatchIdRef.current = null;
    }

    if (locationUpdateTimerRef.current) {
      clearInterval(locationUpdateTimerRef.current);
    }

    try {
      map.off();
      map.remove();
    } catch (e) {
      console.warn('Error cleaning up map:', e);
    }
    
    mapRef.current = null;
    tileLayerRef.current = null;
    setMapReady(false);
  }
};

} catch (error) {
console.error('❌ Error initializing map:', error);
setMapReady(false);
setMapLoading(false);
setMapError('Failed to initialize map. Please refresh the page.');
}
}, [mapCenter, nightMode]);

// CLEAN: Update order markers with team filtering and marker refresh trigger
useEffect(() => {
if (!mapRef.current || !mapReady) return;

const timer = setTimeout(() => {
if (!mapRef.current) return;

// Clear existing order markers
Object.values(orderMarkersRef.current).forEach(markers => {
  if (Array.isArray(markers)) {
    markers.forEach(marker => {
      if (marker && mapRef.current) {
        try {
          mapRef.current.removeLayer(marker);
        } catch (e) {
          console.error('Error removing order marker:', e);
        }
      }
    });
  } else if (markers && mapRef.current) {
    try {
      mapRef.current.removeLayer(markers);
    } catch (e) {
      console.error('Error removing order marker:', e);
    }
  }
});
orderMarkersRef.current = {};

// Filter orders for current team only
const ordersToShow = orders.filter((order) => {
  return shouldShowOrderOnMap(order, team?.id);
});

const orderMarkersToFit = [];

ordersToShow.forEach((order) => {
  const markers = createOrderMarkers(order);
  if (markers && markers.length > 0 && mapRef.current) {
    try {
      markers.forEach(marker => {
        marker.addTo(mapRef.current);
        orderMarkersToFit.push(marker);
      });
      orderMarkersRef.current[order.id] = markers;
    } catch (e) {
      console.error('Error adding order markers:', e);
    }
  }
});
}, 100);

return () => clearTimeout(timer);
}, [orders, mapReady, createOrderMarkers, selectedOrderId, team?.id, markerRefreshTrigger]);

// FIXED: Update vehicle markers with personal vehicle filtering and marker refresh trigger
useEffect(() => {
if (!mapRef.current || !mapReady) return;

const timer = setTimeout(() => {
if (!mapRef.current) return;

console.log(`🚗 Updating vehicle markers - Personal: ${vehicles?.length || 0}, Team: ${teamVehicles?.length || 0}`);

// Clear existing markers
Object.values(vehicleMarkersRef.current).forEach(marker => {
  if (marker && mapRef.current) {
    try {
      mapRef.current.removeLayer(marker);
    } catch (e) {
      console.error('Error removing marker:', e);
    }
  }
});
vehicleMarkersRef.current = {};
geocodingQueue.current = [];

// Filter team vehicles (always show unless recently secured)
const teamVehiclesToShow = (teamVehicles || []).filter(vehicle => {
  // Log each vehicle for debugging
  console.log(`🔍 Team vehicle: ${vehicle.vehicle || vehicle.vin} - Status: ${vehicle.status} - Has location: ${!!(vehicle.position || vehicle.address || (vehicle.lat && vehicle.lng))}`);

  if (vehicle.status === 'SECURED' && securedVehicles.has(vehicle.uniqueKey)) {
    const securedTime = securedVehicles.get(vehicle.uniqueKey);
    const tenMinutes = 10 * 60 * 1000;
    const now = Date.now();
    return now - securedTime < tenMinutes;
  }
  return true;
});

// Combine vehicles - only include personal vehicles if toggle is on
const allVehiclesToShow = [
  ...teamVehiclesToShow,
  ...(vehicles || []) // Personal vehicles controlled by parent prop
];

console.log(`📍 Total vehicles to show on map: ${allVehiclesToShow.length}`);

const markersToFit = [];

allVehiclesToShow.forEach((vehicle, index) => {
  const uniqueKey = vehicle.uniqueKey || `vehicle_${index}`;
  
  const marker = createVehicleMarker(vehicle);
  if (marker && mapRef.current) {
    try {
      marker.addTo(mapRef.current);
      vehicleMarkersRef.current[uniqueKey] = marker;
      markersToFit.push(marker);
    } catch (e) {
      console.error('Error adding marker:', e);
    }
  }
});

if (geocodingQueue.current.length > 0) {
  processGeocodingQueue();
}

// Only fit bounds if this is the first load or vehicle count significantly changed
const shouldFitBounds = !mapInitializedRef.current || 
                        (lastVehicleCountRef.current === 0 && allVehiclesToShow.length > 0) ||
                        Math.abs(allVehiclesToShow.length - lastVehicleCountRef.current) > 5;

if (mapRef.current && shouldFitBounds && (markersToFit.length > 0 || currentLocationMarkerRef.current)) {
  try {
    const allOrderMarkers = Object.values(orderMarkersRef.current).flat();
    const group = new L.featureGroup([...markersToFit, ...allOrderMarkers]);
    if (currentLocationMarkerRef.current) {
      group.addLayer(currentLocationMarkerRef.current);
    }
    
    if (group.getLayers().length > 0) {
      try {
        const bounds = group.getBounds();
        mapRef.current.fitBounds(bounds.pad(0.1), {
          maxZoom: 15
        });
        mapInitializedRef.current = true;
      } catch (error) {
        console.error('Error fitting bounds:', error);
        mapRef.current.setView([mapCenter.lat, mapCenter.lng], 8);
      }
    }
  } catch (e) {
    console.error('Error with feature group:', e);
    mapRef.current.setView([mapCenter.lat, mapCenter.lng], 8);
  }
}

lastVehicleCountRef.current = allVehiclesToShow.length;
}, 100);

return () => clearTimeout(timer);
}, [vehicles, teamVehicles, mapReady, createVehicleMarker, processGeocodingQueue, mapCenter, securedVehicles, selectedVehicleId, markerRefreshTrigger]);

// ADDITIONAL: Force marker update when team vehicles are first loaded
useEffect(() => {
  if (mapReady && teamVehicles && teamVehicles.length > 0 && Object.keys(vehicleMarkersRef.current).length === 0) {
    console.log('🚀 Team vehicles loaded but no markers present - forcing refresh');
    setTimeout(() => {
      setMarkerRefreshTrigger(prev => prev + 1);
    }, 200);
  }
}, [mapReady, teamVehicles]);

// Update trails on map (using passed trail state)
const updateTrails = useCallback(() => {
if (!mapRef.current) return;

Object.values(trailLayersRef.current).forEach(layer => {
if (layer && mapRef.current) {
  mapRef.current.removeLayer(layer);
}
});
trailLayersRef.current = {};

if (!showTrails || !locationHistory) {
return;
}

let trailCount = 0;
locationHistory.forEach((history, userId) => {
const trail = createTrailPolyline(userId, history);
if (trail && mapRef.current) {
  trail.addTo(mapRef.current);
  trailLayersRef.current[userId] = trail;
  trailCount++;
}
});
}, [mapRef, showTrails, locationHistory, createTrailPolyline, trailTimeRange]);

// Update teammate markers (using passed teammate state)
const updateTeammateMarkers = useCallback(() => {
if (!mapRef.current) return;

Object.values(teammateMarkersRef.current).forEach(marker => {
if (marker && mapRef.current) {
  mapRef.current.removeLayer(marker);
}
});
teammateMarkersRef.current = {};

if (!teammateLocations) return;

let markerCount = 0;
teammateLocations.forEach((location, userId) => {
const marker = createTeammateMarker(location);
if (marker && mapRef.current) {
  marker.addTo(mapRef.current);
  teammateMarkersRef.current[userId] = marker;
  markerCount++;
}
});
}, [mapRef, teammateLocations, createTeammateMarker]);

// Start location tracking on mount
useEffect(() => {
if (mapReady) {
startLocationTracking();
}

return () => {
stopLocationTracking();
};
}, [mapReady, startLocationTracking, stopLocationTracking]);

// Load profiles when team members are available
useEffect(() => {
if (allTeamMembers?.size > 0 && !profilesLoading && !profilesLoaded) {
loadTeamMemberProfiles();
}
}, [allTeamMembers?.size, loadTeamMemberProfiles, profilesLoading, profilesLoaded]);

// Update trails when time range changes
useEffect(() => {
if (mapReady) {
updateTrails();
}
}, [mapReady, showTrails, trailTimeRange, updateTrails]);

// Update teammate markers when locations change
useEffect(() => {
if (mapReady) {
updateTeammateMarkers();
}
}, [mapReady, teammateLocations, updateTeammateMarkers]);

// Update trails when location history changes
useEffect(() => {
if (mapReady) {
updateTrails();
}
}, [mapReady, showTrails, locationHistory, updateTrails]);

// MOBILE TRACKING: Enhanced periodic location updates every 5 seconds
useEffect(() => {
if (!user || !db || !team) return;

const interval = setInterval(() => {
if (currentLocation) {
  console.log('⏰ PERIODIC LOCATION UPDATE:', currentLocation);
  storeLocation(currentLocation);
}
}, LOCATION_UPDATE_INTERVAL); // 5 seconds instead of 30

return () => clearInterval(interval);
}, [currentLocation, storeLocation, user, db, team]);

// Cleanup on unmount
useEffect(() => {
return () => {
if (profileLoadingTimeoutRef.current) {
  clearTimeout(profileLoadingTimeoutRef.current);
}
if (locationWatchIdRef.current) {
  navigator.geolocation.clearWatch(locationWatchIdRef.current);
}
if (locationUpdateTimerRef.current) {
  clearInterval(locationUpdateTimerRef.current);
}
};
}, []);

// CLEAN: Vehicle and Order statistics with team filtering
const vehicleStats = useMemo(() => {
const teamOrdersFiltered = orders.filter(o => o.teamId === team?.id);

const orderStats = {
total: teamOrdersFiltered.length,
visible: teamOrdersFiltered.filter(o => shouldShowOrderOnMap(o, team?.id)).length,
byStatus: {},
bySecure: {
  secure: teamOrdersFiltered.filter(o => o.secure === true).length,
  notSecure: teamOrdersFiltered.filter(o => o.secure === false).length,
  undefined: teamOrdersFiltered.filter(o => o.secure === undefined || o.secure === null).length
},
open: teamOrdersFiltered.filter(o => {
  const status = (o.status || '').toLowerCase();
  return status === 'open' || status === 'open-order' || status === 'pending';
}).length,
secure: teamOrdersFiltered.filter(o => o.status === 'secure' || o.secure === true).length,
pendingPickup: teamOrdersFiltered.filter(o => o.status === 'pending-pickup' || o.status === 'awaiting-pickup').length,
claim: teamOrdersFiltered.filter(o => o.status === 'claim').length,
restricted: teamOrdersFiltered.filter(o => o.status === 'restricted').length,
withLocation: teamOrdersFiltered.filter(o => 
  (o.position && o.position.lat && o.position.lng) ||
  (o.addresses && o.addresses.some(addr => addr.position && addr.position.lat && addr.position.lng))
).length,
needsGeocoding: teamOrdersFiltered.filter(o => 
  o.addresses && o.addresses.length > 0 && 
  !o.position && 
  !o.addresses.some(addr => addr.position && addr.position.lat && addr.position.lng)
).length,
multipleAddresses: teamOrdersFiltered.filter(o => o.addresses && o.addresses.length > 1).length
};

// Count orders by status
teamOrdersFiltered.forEach(order => {
const status = order.status || 'undefined';
orderStats.byStatus[status] = (orderStats.byStatus[status] || 0) + 1;
});

return {
personal: {
  total: vehicles?.length || 0,
  secured: vehicles?.filter(v => v.status === 'SECURED').length || 0,
  found: vehicles?.filter(v => v.status === 'FOUND').length || 0,
  pending: vehicles?.filter(v => v.status === 'PENDING PICKUP').length || 0,
  withLocation: vehicles?.filter(v => 
    (v.position && v.position.lat && v.position.lng) || 
    (v.lat && v.lng)
  ).length || 0
},
team: {
  total: teamVehicles.length,
  secured: teamVehicles.filter(v => v.status === 'SECURED').length,
  found: teamVehicles.filter(v => v.status === 'FOUND').length,
  pending: teamVehicles.filter(v => v.status === 'PENDING PICKUP').length,
  doNotSecure: teamVehicles.filter(v => v.status === 'DO NOT SECURE').length,
  notFound: teamVehicles.filter(v => v.status === 'NOT FOUND').length,
  bottomStatus: teamVehicles.filter(v => v.bottomStatus).length,
  withLocation: teamVehicles.filter(v => 
    (v.position && v.position.lat && v.position.lng) || 
    (v.lat && v.lng)
  ).length,
  needsGeocoding: teamVehicles.filter(v => 
    (v.address || v.city || v.fullAddress) && 
    !((v.position && v.position.lat && v.position.lng) || (v.lat && v.lng))
  ).length
},
orders: orderStats
};
}, [vehicles, teamVehicles, orders, team?.id]);

// UPDATED: Get online/offline counts for compact display
const teammateStats = useMemo(() => {
if (!teammateColors || !teammateLocations) {
return { onlineCount: 0, offlineCount: 0, totalCount: 0 };
}

let onlineCount = 0;
let offlineCount = 0;

teammateColors.forEach((color, userId) => {
const location = teammateLocations.get(userId);
const isOnline = location && location.timestamp && (Date.now() - location.timestamp.getTime()) < 10 * 60 * 1000;

if (isOnline) {
  onlineCount++;
} else {
  offlineCount++;
}
});

return { onlineCount, offlineCount, totalCount: teammateColors.size };
}, [teammateColors, teammateLocations]);

return (
<>
<div className="bg-gray-900 bg-opacity-90 backdrop-blur-lg rounded-xl shadow-xl border border-gray-700 p-4 mb-4">
  <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3 gap-2">
    <h3 className="text-lg font-bold text-white flex flex-wrap items-center gap-2">
      <span className="mr-2 text-2xl">🗺️</span>
      <span>Team Vehicle & Orders Map</span>
      
      {/* Team Name Display */}
      {team?.name && (
        <span className="bg-indigo-600 text-white text-xs px-2 py-1 rounded-full">
          {team.name}
        </span>
      )}
      
      {/* MOBILE TRACKING: GPS Status Display */}
      {gpsStatus && (
        <span className={`text-white text-xs px-2 py-1 rounded-full ${
          gpsStatus.includes('✅') ? 'bg-green-600' : 
          gpsStatus.includes('❌') ? 'bg-red-600' : 
          'bg-blue-600'
        }`}>
          {gpsStatus}
        </span>
      )}
      
      {/* FIXED: Night Mode Indicator */}
      {nightMode && (
        <span className="bg-purple-600 text-white text-xs px-2 py-1 rounded-full animate-pulse">
          🌙 Night Mode Active
        </span>
      )}
      
      {/* Personal Vehicle Stats */}
      {vehicleStats.personal.total > 0 && (
        <span className="bg-blue-600 text-white text-xs px-2 py-1 rounded-full">
          {vehicleStats.personal.total} personal vehicles
        </span>
      )}
      
      {/* Team Vehicle Stats */}
      <span className="bg-purple-600 text-white text-xs px-2 py-1 rounded-full">
        {vehicleStats.team.total} team vehicles
      </span>
      {vehicleStats.team.withLocation > 0 && (
        <span className="bg-green-600 text-white text-xs px-2 py-1 rounded-full">
          {vehicleStats.team.withLocation} with GPS
        </span>
      )}
      {vehicleStats.team.needsGeocoding > 0 && (
        <span className="bg-yellow-600 text-white text-xs px-2 py-1 rounded-full animate-pulse">
          {vehicleStats.team.needsGeocoding} geocoding
        </span>
      )}
      
      {/* Order Stats */}
      <span className="bg-blue-600 text-white text-xs px-2 py-1 rounded-full">
        {vehicleStats.orders.total} orders
      </span>
      {vehicleStats.orders.visible > 0 && (
        <span className="bg-indigo-600 text-white text-xs px-2 py-1 rounded-full">
          {vehicleStats.orders.visible} visible
        </span>
      )}
      {vehicleStats.orders.secure > 0 && (
        <span className="bg-green-700 text-white text-xs px-2 py-1 rounded-full">
          {vehicleStats.orders.secure} secured
        </span>
      )}
      {vehicleStats.orders.withLocation > 0 && (
        <span className="bg-cyan-600 text-white text-xs px-2 py-1 rounded-full">
          {vehicleStats.orders.withLocation} orders with GPS
        </span>
      )}
      {vehicleStats.orders.multipleAddresses > 0 && (
        <span className="bg-orange-600 text-white text-xs px-2 py-1 rounded-full">
          {vehicleStats.orders.multipleAddresses} multi-address
        </span>
      )}
      
      {vehicleStats.team.bottomStatus > 0 && (
        <span className="bg-red-600 text-white text-xs px-2 py-1 rounded-full animate-pulse">
          {vehicleStats.team.bottomStatus} bottom status
        </span>
      )}
      {profilesLoading && (
        <span className="bg-blue-600 text-white text-xs px-2 py-1 rounded-full animate-pulse">
          Loading profiles...
        </span>
      )}
      {profilesLoaded && (
        <span className="bg-cyan-600 text-white text-xs px-2 py-1 rounded-full">
          ✅ {teamMemberProfiles.size} profiles loaded
        </span>
      )}
      {isCurrentUserAdmin && (
        <span className="bg-red-600 text-white text-xs px-2 py-1 rounded-full">
          🔒 Admin
        </span>
      )}
    </h3>
    
    <div className="flex items-center gap-2">
      <div className="hidden sm:flex items-center space-x-2 text-xs">
        {/* Vehicle Legend */}
        <span className="flex items-center">
          <div className="w-3 h-3 bg-green-500 rounded-full mr-1"></div>
          Secured
        </span>
        <span className="flex items-center">
          <div className="w-3 h-3 bg-yellow-500 rounded-full mr-1"></div>
          Found
        </span>
        <span className="flex items-center">
          <div className="w-3 h-3 bg-blue-500 rounded-full mr-1"></div>
          Pending
        </span>
        <span className="flex items-center">
          <div className="w-3 h-3 bg-red-500 rounded-full mr-1"></div>
          Bottom Status
        </span>
        
        {/* Order Legend */}
        <span className="border-l border-gray-500 pl-2 ml-2 flex items-center">
          <div className="w-3 h-3 bg-blue-600 rounded mr-1" style={{borderRadius: '3px'}}></div>
          Orders
        </span>
        <span className="flex items-center">
          <div className="w-3 h-3 bg-orange-600 rounded mr-1" style={{borderRadius: '3px', border: '2px solid #FFD700'}}></div>
          Multi-Address
        </span>
      </div>
    </div>
  </div>

  <div className="mb-3 text-sm text-yellow-200">
    <span className="inline-flex items-center bg-yellow-600 bg-opacity-50 px-3 py-1 rounded-lg">
      <span className="mr-2">👆</span>
      Click any vehicle or order icon to view details and actions. Camera car drivers can check in on orders. Tow truck drivers can secure vehicles.
      {vehicleStats.orders.secure > 0 && (
        <span className="ml-2 text-green-300">✅ Secured orders are hidden from map but still visible in Orders panel.</span>
      )}
      <span className="ml-2 text-blue-300">🏢 Showing only {team?.name || 'current team'} data.</span>
      {isCurrentUserAdmin && (
        <span className="ml-2 text-red-300">🔒 Admin: Can delete location trails.</span>
      )}
      {nightMode && (
        <span className="ml-2 text-purple-300">🌙 Night mode active - using dark map tiles for easier viewing in low light.</span>
      )}
      {/* MOBILE TRACKING: GPS Status in help text */}
      {gpsStatus && (
        <span className="ml-2 text-cyan-300">📍 GPS: {gpsStatus}</span>
      )}
    </span>
  </div>

  <div className="relative">
    <div
      ref={mapContainerRef}
      className={`vehicle-map-container rounded-lg overflow-hidden map-container-wrapper ${mapContainerClass} ${nightMode ? 'night-mode' : ''}`}
    ></div>

    {/* Loading indicator */}
    {mapLoading && (
      <div className="absolute inset-0 bg-gray-100 bg-opacity-75 flex items-center justify-center z-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading map...</p>
        </div>
      </div>
    )}

    {/* Error message */}
    {mapError && (
      <div className="absolute top-4 left-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm">{mapError}</p>
          </div>
          <div className="ml-auto pl-3">
            <button
              onClick={() => setMapError(null)}
              className="text-red-400 hover:text-red-600"
            >
              <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    )}
    
    {/* NEW: Alert Component Integration */}
    <AlertComp 
      currentUser={user}
      isAdmin={isCurrentUserAdmin}
      teamId={team?.id}
      setMapContainerClass={setMapContainerClass}
    />
    
    {/* Vehicle/Order selection notification */}
    {vehicleSelectionNotification && (
      <div className="vehicle-selection-notification">
        {vehicleSelectionNotification.type === 'order' ? '📋' : '🚗'} Selected: {vehicleSelectionNotification.vehicle} 
        <br />
        <small>
          VIN: {vehicleSelectionNotification.vin}
        </small>
      </div>
    )}
    
    {(vehicleStats.team.total === 0 && vehicleStats.orders.visible === 0 && vehicleStats.personal.total === 0) && (
      <div className="absolute inset-0 flex items-center justify-center bg-gray-900 bg-opacity-75 rounded-lg">
        <div className="text-center p-4">
          <p className="text-yellow-400 font-semibold mb-2">No vehicles or open orders to display for {team?.name || 'this team'}</p>
          <p className="text-gray-300 text-sm">
            Team vehicles and open orders will appear here
            {vehicleStats.orders.secure > 0 && (
              <span className="block mt-1 text-green-400">
                ({vehicleStats.orders.secure} secured orders are hidden)
              </span>
            )}
          </p>
        </div>
      </div>
    )}
    
    {geocodingStatus && (
      <div className="geocoding-status">
        <span>{geocodingStatus}</span>
      </div>
    )}
    
    {/* UPDATED: Enhanced Map Controls with Refresh Button and GPS Test */}
    <div className="map-controls">
      <button
        className={`map-control-button location-btn ${currentLocation ? 'active' : ''}`}
        onClick={findMyLocation}
        title="Find my location"
        disabled={!mapReady}
      >
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
          <path d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      </button>
      
      {/* MOBILE TRACKING: GPS Test Button */}
      <button
        className="map-control-button gps-test-btn"
        onClick={testGPS}
        title="Test GPS functionality"
        disabled={!mapReady}
      >
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <circle cx="12" cy="12" r="10"/>
          <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/>
          <path d="M12 17h.01"/>
        </svg>
      </button>
      
      {/* FIXED: Night Mode Toggle Button with Working Implementation */}
      <button
        className={`map-control-button night-mode-toggle ${nightMode ? 'active' : ''}`}
        onClick={toggleNightMode}
        title={nightMode ? "Switch to Day Mode" : "Switch to Night Mode"}
        disabled={!mapReady}
      >
        {nightMode ? (
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <circle cx="12" cy="12" r="5"/>
            <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
          </svg>
        ) : (
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
          </svg>
        )}
      </button>

      {/* NEW: Refresh Button */}
      <button
        className={`map-control-button refresh-btn ${isRefreshing ? 'refreshing' : ''}`}
        onClick={refreshMapData}
        title="Refresh map data"
        disabled={!mapReady || isRefreshing}
      >
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <polyline points="23 4 23 10 17 10"/>
          <polyline points="1 20 1 14 7 14"/>
          <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"/>
        </svg>
      </button>
    </div>

    {/* MOBILE TRACKING: Location Error Display */}
    {locationErrors.length > 0 && (
      <div className="location-errors">
        <div className="location-errors-title">📍 GPS Errors:</div>
        {locationErrors.map((error, index) => (
          <div key={index} className="location-error-item">
            {error}
          </div>
        ))}
      </div>
    )}
  </div>
</div>

{/* Order Details Popup with Camera Car Functionality */}
{showOrderPopup && selectedOrder && (
  <OrderPopup
    order={selectedOrder}
    onClose={() => {
      setShowOrderPopup(false);
      setSelectedOrder(null);
    }}
    onNavigate={handleOrderNavigate}
    onSecure={handleOrderSecure}
    currentUser={user}
    db={db}
    team={team}
    userProfile={userProfile}
    onTeamVehiclesUpdate={onTeamVehiclesUpdate}
  />
)}

{/* Enhanced Delete Confirmation Modal with Time Range Support */}
{showDeleteConfirmation && (
  <DeleteConfirmationModal
    isOpen={showDeleteConfirmation}
    onClose={() => {
      if (!isDeleting) {
        setShowDeleteConfirmation(false);
      }
    }}
    onConfirm={() => {
      if (onDeleteAllTrails && typeof onDeleteAllTrails === 'function') {
        onDeleteAllTrails();
      }
    }}
    type={deleteType}
    userName={deleteUserName}
    isDeleting={isDeleting}
    deleteProgress={deleteProgress}
    trailCount={0} // This would be calculated in parent
    selectedTimeRange={deleteTimeRange}
  />
)}

{/* EDIT ADDRESS MODAL (keeping existing functionality) */}
{editingMapVehicle && (
  <div className="fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center p-4">
    <div className="bg-gray-900 rounded-xl p-6 max-w-lg w-full border border-blue-600 shadow-2xl max-h-[90vh] overflow-y-auto">
      <h3 className="text-xl font-bold text-blue-400 mb-4 flex items-center">
        <span className="mr-2">✏️</span>
        Edit Address for {editingMapVehicle.vehicle}
      </h3>
      
      <div className="mb-4 p-3 bg-blue-900 bg-opacity-30 rounded-lg border border-blue-600">
        <p className="text-blue-300 text-sm">
          <strong>VIN:</strong> {editingMapVehicle.vin} • <strong>From:</strong> {editingMapVehicle.teamMemberName}
        </p>
        <p className="text-blue-200 text-xs mt-1">
          Fix address components and re-geocode to get accurate map location
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-gray-300 text-sm font-semibold mb-2">
            Street Address
          </label>
          <input
            type="text"
            value={editMapVehicleData.address}
            onChange={(e) => setEditMapVehicleData(prev => ({ ...prev, address: e.target.value }))}
            className="w-full bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm focus:border-blue-500 focus:outline-none"
            placeholder="e.g., 123 Main St"
          />
        </div>

        <div className="grid grid-cols-2 gap-3">
          <div>
            <label className="block text-gray-300 text-sm font-semibold mb-2">
              City *
            </label>
            <input
              type="text"
              value={editMapVehicleData.city}
              onChange={(e) => setEditMapVehicleData(prev => ({ ...prev, city: e.target.value }))}
              className="w-full bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm focus:border-blue-500 focus:outline-none"
              placeholder="Chicago"
              required
            />
          </div>

          <div>
            <label className="block text-gray-300 text-sm font-semibold mb-2">
              State *
            </label>
            <select
              value={editMapVehicleData.state}
              onChange={(e) => setEditMapVehicleData(prev => ({ ...prev, state: e.target.value }))}
              className="w-full bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm focus:border-blue-500 focus:outline-none"
              required
            >
              <option value="">Select State</option>
              <option value="IL">Illinois (IL)</option>
              <option value="IN">Indiana (IN)</option>
              <option value="WI">Wisconsin (WI)</option>
              <option value="MI">Michigan (MI)</option>
              <option value="IA">Iowa (IA)</option>
              <option value="MO">Missouri (MO)</option>
              <option value="MN">Minnesota (MN)</option>
              <option value="OH">Ohio (OH)</option>
              <option value="KY">Kentucky (KY)</option>
            </select>
          </div>
        </div>

        <div>
          <label className="block text-gray-300 text-sm font-semibold mb-2">
            ZIP Code
          </label>
          <input
            type="text"
            value={editMapVehicleData.zipCode}
            onChange={(e) => setEditMapVehicleData(prev => ({ ...prev, zipCode: e.target.value }))}
            className="w-full bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm focus:border-blue-500 focus:outline-none"
            placeholder="60601"
            maxLength={10}
          />
        </div>

        {(editMapVehicleData.address || editMapVehicleData.city || editMapVehicleData.state) && (
          <div className="bg-green-900 bg-opacity-30 rounded-lg p-3 border border-green-600">
            <p className="text-green-300 text-xs font-semibold mb-1">📍 New Address Preview:</p>
            <p className="text-white font-semibold text-sm">
              {buildFullAddress(editMapVehicleData.address, editMapVehicleData.city, editMapVehicleData.state, editMapVehicleData.zipCode)}
            </p>
          </div>
        )}
      </div>

      <div className="flex space-x-3 mt-6">
        <button
          onClick={handleSaveMapVehicle}
          disabled={savingMapEdit || !editMapVehicleData.city || !editMapVehicleData.state}
          className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white py-3 rounded-lg font-bold shadow-lg transition-all flex items-center justify-center"
        >
          {savingMapEdit ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
              Saving & Geocoding...
            </>
          ) : (
            <>
              <span className="mr-2">💾</span>
              Save & Re-Geocode
            </>
          )}
        </button>
        <button
          onClick={() => {
            setEditingMapVehicle(null);
            setEditMapVehicleData({});
          }}
          disabled={savingMapEdit}
          className="flex-1 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-700 text-white py-3 rounded-lg font-bold shadow-lg transition-all"
        >
          Cancel
        </button>
      </div>

      <div className="mt-4 p-3 bg-yellow-900 bg-opacity-30 rounded-lg border border-yellow-600">
        <p className="text-yellow-300 text-xs">
          <strong>⚠️ Important:</strong> This will update the original vehicle in the database and re-geocode the location for more accurate mapping. City and State are required for proper geocoding within IL/IN/WI region.
        </p>
      </div>
    </div>
  </div>
)}
</>
);
};

export default VehicleMapDisplay;