// StandaloneVehicleTracker.js - ENHANCED FULL-SCREEN MAP WITH CENTERED MOBILE-FRIENDLY OVERLAY FEATURES

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { collection, getDocs, doc, getDoc, query, orderBy, setDoc, updateDoc, serverTimestamp, addDoc, deleteDoc, onSnapshot, where, writeBatch, limit } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';

// Import Team Vehicle Tracker Component
import TeamVehicleTracker from './TeamVehicleTracker';

// Import Vehicle Map Display Component
import VehicleMapDisplay from './VehicleMapDisplay';

// Import Statistics Module
import { 
  calculateWeeklyStats, 
  calculateMonthAndYTDStats, 
  StatsCards, 
  MonthlyAndYTDStats 
} from './VehicleTrackerStats';

// Import utilities
import {
  loadPDFLibraries,
  getUserIdFromUrl,
  getUserAvatar,
  getCurrentWeekId,
  formatDateRange,
  formatNumber,
  getStatusColor,
  getStatusEmoji,
  getNotificationColor,
  playNotificationSound,
  playMoneySound,
  playReminderSound,
  compressImage,
  getGeolocation,
  navigateToAddress,
  calculateElapsedTime,
  getTimerDisplay,
  checkUserPermissions,
  suppressGoogleAPIErrors
} from './VehicleTrackerUtils';

// Import Firebase operations
import {
  initializeFirebase,
  loadUserData,
  findUserTeam,
  createWeekIfNeeded,
  initializeWeekWithCarryOvers,
  updateVehicleWeekStats,
  markVINAsSecuredAcrossTeam,
  handleImageUpload,
  loadNeverSecuredVehicles,
  markTeamVehicleBottomStatus,
  recheckTeamVehicle
} from './VehicleTrackerFirebase';

// Import Slack integration
import { postVehicleToSlack, formatVehicleForSlack } from './slackIntegration';

// Enhanced viewport initialization for mobile
const initializeViewport = () => {
  // Remove any existing viewport meta tags
  const existingViewport = document.querySelector('meta[name="viewport"]');
  if (existingViewport) {
    existingViewport.remove();
  }

  // Create new viewport meta tag with proper mobile settings
  const viewport = document.createElement('meta');
  viewport.name = 'viewport';
  viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover';
  document.head.appendChild(viewport);

  // Add mobile web app capable meta tags
  const mobileCapable = document.createElement('meta');
  mobileCapable.name = 'mobile-web-app-capable';
  mobileCapable.content = 'yes';
  document.head.appendChild(mobileCapable);

  const appleCapable = document.createElement('meta');
  appleCapable.name = 'apple-mobile-web-app-capable';
  appleCapable.content = 'yes';
  document.head.appendChild(appleCapable);

  const statusBar = document.createElement('meta');
  statusBar.name = 'apple-mobile-web-app-status-bar-style';
  statusBar.content = 'black-translucent';
  document.head.appendChild(statusBar);

  // Prevent zoom on input focus for iOS
  const handleTouchStart = (e) => {
    if (e.target.nodeName === 'INPUT' || e.target.nodeName === 'TEXTAREA') {
      e.target.style.fontSize = '16px';
    }
  };
  document.addEventListener('touchstart', handleTouchStart);

  // Add mobile-specific styles
  const style = document.createElement('style');
  style.innerHTML = `
    * {
      -webkit-tap-highlight-color: transparent;
      -webkit-touch-callout: none;
    }
    
    body {
      -webkit-text-size-adjust: 100%;
      -ms-text-size-adjust: 100%;
      text-size-adjust: 100%;
      overscroll-behavior: contain;
    }
    
    input, textarea, select {
      font-size: 16px !important;
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
    }
    
    input[type="date"] {
      min-height: 44px;
    }
    
    /* Prevent horizontal scroll */
    html, body {
      overflow-x: hidden;
      position: relative;
    }
    
    /* Ensure all containers respect viewport */
    .container {
      max-width: 100vw;
      overflow-x: hidden;
    }
    
    /* Fix for iOS input zoom */
    @media screen and (-webkit-min-device-pixel-ratio: 0) {
      input:focus,
      textarea:focus,
      select:focus {
        font-size: 16px !important;
      }
    }
    
    /* Ensure modals work on mobile */
    .fixed {
      -webkit-backface-visibility: hidden;
      backface-visibility: hidden;
    }

    /* UPDATED: Centered mobile-friendly overlay styles */
    .features-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.8);
      backdrop-filter: blur(5px);
      z-index: 1000;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
    }

    .features-selector {
      background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
      border-radius: 20px;
      padding: 30px;
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
      border: 2px solid #4f46e5;
      max-width: 600px;
      width: 100%;
      max-height: 80vh;
      overflow-y: auto;
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
      gap: 15px;
      margin-top: 20px;
    }

    .feature-card {
      background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
      border-radius: 15px;
      padding: 20px;
      border: 2px solid transparent;
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: center;
      position: relative;
      overflow: hidden;
    }

    .feature-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.5);
      border-color: #3b82f6;
    }

    .feature-card.active {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      border-color: #60a5fa;
      box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.7);
    }

    .feature-card.active::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
      pointer-events: none;
    }

    .feature-icon {
      font-size: 2.5rem;
      margin-bottom: 10px;
      display: block;
    }

    .feature-title {
      font-weight: bold;
      color: white;
      margin-bottom: 5px;
      font-size: 1.1rem;
    }

    .feature-description {
      font-size: 0.85rem;
      color: #d1d5db;
      line-height: 1.4;
    }

    .feature-card.active .feature-description {
      color: #e0e7ff;
    }

    /* UPDATED: All panels are now centered and mobile-friendly */
    .overlay-panel {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(17, 24, 39, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 15px;
      border: 1px solid rgba(75, 85, 99, 0.5);
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
      z-index: 500;
      width: 90vw;
      height: 85vh;
      max-width: 800px;
      max-height: 700px;
      overflow: hidden;
      transition: all 0.3s ease;
      opacity: 0;
      transform: translate(-50%, -50%) scale(0.9);
      pointer-events: none;
    }

    .overlay-panel.visible {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
      pointer-events: all;
    }

    .overlay-panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 25px;
      border-bottom: 1px solid rgba(75, 85, 99, 0.3);
      background: rgba(55, 65, 81, 0.7);
      border-radius: 15px 15px 0 0;
      flex-shrink: 0;
    }

    .overlay-panel-title {
      font-weight: bold;
      color: white;
      font-size: 1.2rem;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .overlay-panel-close {
      background: rgba(239, 68, 68, 0.8);
      border: none;
      color: white;
      width: 36px;
      height: 36px;
      border-radius: 50%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 18px;
      transition: all 0.2s ease;
    }

    .overlay-panel-close:hover {
      background: rgba(239, 68, 68, 1);
      transform: scale(1.1);
    }

    .overlay-panel-content {
      padding: 25px;
      overflow-y: auto;
      height: calc(100% - 80px);
    }

    /* Chat specific styles */
    .chat-container {
      display: flex;
      flex-direction: column;
      height: 100%;
    }

    .chat-messages {
      flex: 1;
      overflow-y: auto;
      padding: 15px;
      background: rgba(31, 41, 55, 0.5);
      border-radius: 10px;
      margin-bottom: 15px;
      max-height: 400px;
    }

    .chat-message {
      display: flex;
      gap: 10px;
      margin-bottom: 15px;
      padding: 10px;
      border-radius: 10px;
      background: rgba(55, 65, 81, 0.3);
    }

    .chat-message.own {
      background: rgba(59, 130, 246, 0.2);
      margin-left: 20px;
    }

    .chat-avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: #4B5563;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 14px;
      flex-shrink: 0;
    }

    .chat-content {
      flex: 1;
    }

    .chat-sender {
      font-weight: bold;
      color: #60A5FA;
      font-size: 0.875rem;
      margin-bottom: 2px;
    }

    .chat-text {
      color: #E5E7EB;
      font-size: 0.875rem;
      line-height: 1.4;
      margin-bottom: 4px;
    }

    .chat-timestamp {
      color: #9CA3AF;
      font-size: 0.75rem;
    }

    .chat-input-container {
      display: flex;
      gap: 10px;
      align-items: end;
    }

    .chat-input {
      flex: 1;
      background: rgba(31, 41, 55, 0.8);
      border: 1px solid #4B5563;
      border-radius: 10px;
      padding: 10px 15px;
      color: white;
      resize: none;
      min-height: 40px;
      max-height: 100px;
    }

    .chat-input:focus {
      outline: none;
      border-color: #60A5FA;
    }

    .chat-send-btn {
      background: #3B82F6;
      border: none;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      color: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .chat-send-btn:hover {
      background: #2563EB;
    }

    .chat-send-btn:disabled {
      background: #4B5563;
      cursor: not-allowed;
    }

    /* Trails specific styles */
    .trails-container {
      height: 100%;
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .trails-controls {
      background: rgba(31, 41, 55, 0.5);
      border-radius: 10px;
      padding: 15px;
    }

    .trails-stats {
      background: rgba(31, 41, 55, 0.5);
      border-radius: 10px;
      padding: 15px;
      flex: 1;
      overflow-y: auto;
    }

    .trails-time-selector {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
      margin-bottom: 15px;
    }

    .trails-time-btn {
      background: rgba(55, 65, 81, 0.8);
      border: 1px solid #4B5563;
      color: white;
      padding: 8px 12px;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: 0.875rem;
    }

    .trails-time-btn:hover {
      background: rgba(75, 85, 99, 0.8);
    }

    .trails-time-btn.active {
      background: #3B82F6;
      border-color: #60A5FA;
    }

    .trails-member-list {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .trails-member-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px;
      background: rgba(55, 65, 81, 0.3);
      border-radius: 8px;
    }

    .trails-member-info {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .trails-member-color {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      border: 2px solid white;
    }

    .trails-member-name {
      color: white;
      font-weight: 500;
    }

    .trails-member-status {
      font-size: 0.75rem;
      color: #9CA3AF;
    }

    .trails-member-actions {
      display: flex;
      gap: 5px;
    }

    .trails-action-btn {
      background: transparent;
      border: 1px solid;
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.75rem;
      transition: all 0.2s ease;
    }

    .trails-toggle-btn {
      border-color: #10B981;
      color: #10B981;
    }

    .trails-toggle-btn:hover {
      background: #10B981;
      color: white;
    }

    .trails-delete-btn {
      border-color: #EF4444;
      color: #EF4444;
    }

    .trails-delete-btn:hover {
      background: #EF4444;
      color: white;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
      .overlay-panel {
        width: 95vw;
        height: 90vh;
        max-width: none;
        max-height: none;
      }

      .overlay-panel-header {
        padding: 15px 20px;
      }

      .overlay-panel-title {
        font-size: 1.1rem;
      }

      .overlay-panel-content {
        padding: 20px;
        height: calc(100% - 70px);
      }

      .features-grid {
        grid-template-columns: repeat(2, 1fr);
      }

      .feature-card {
        padding: 15px;
      }

      .feature-icon {
        font-size: 2rem;
      }
    }

    /* Vehicle Detail Card Styles */
    .vehicle-detail-card {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
      border-radius: 20px;
      padding: 30px;
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
      border: 2px solid #4f46e5;
      max-width: 500px;
      width: 90vw;
      max-height: 80vh;
      overflow-y: auto;
      z-index: 600;
    }

    .vehicle-detail-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.8);
      backdrop-filter: blur(5px);
      z-index: 500;
    }

    @media (max-width: 768px) {
      .vehicle-detail-card {
        width: 95vw;
        padding: 20px;
      }
    }
  `;
  document.head.appendChild(style);
};

// Initialize viewport and libraries on load
initializeViewport();
loadPDFLibraries();

// Common vehicle colors
const VEHICLE_COLORS = [
  'Black', 'White', 'Silver', 'Gray', 'Red', 'Blue', 'Green', 
  'Yellow', 'Orange', 'Brown', 'Gold', 'Beige', 'Tan', 'Burgundy',
  'Purple', 'Pink', 'Teal', 'Navy', 'Charcoal', 'Pearl White',
  'Metallic Silver', 'Dark Blue', 'Light Blue', 'Dark Gray'
];

// DO NOT SECURE reasons
const DO_NOT_SECURE_REASONS = [
  'DO NOT USE LIST',
  'BLOCKED IN',
  'PEOPLE IN VEHICLE',
  'OTHER'
];

// STATE OPTIONS FOR DROPDOWN
const STATE_OPTIONS = [
  { value: 'IL', label: 'Illinois' },
  { value: 'IN', label: 'Indiana' },
  { value: 'WI', label: 'Wisconsin' },
  { value: 'MI', label: 'Michigan' },
  { value: 'IA', label: 'Iowa' },
  { value: 'MO', label: 'Missouri' },
  { value: 'MN', label: 'Minnesota' },
  { value: 'OH', label: 'Ohio' },
  { value: 'KY', label: 'Kentucky' }
];

// Trail time ranges for trails feature
const TRAIL_TIME_RANGES = [
  { label: '1H', value: 1, unit: 'hour' },
  { label: '8H', value: 8, unit: 'hour' },
  { label: '1D', value: 1, unit: 'day' },
  { label: '1W', value: 1, unit: 'week' },
  { label: '1M', value: 1, unit: 'month' }
];

// BUILD FULL ADDRESS HELPER
const buildFullAddress = (address, city, state, zipCode) => {
  const parts = [];
  if (address) parts.push(address);
  if (city) parts.push(city);
  if (state) parts.push(state);
  if (zipCode) parts.push(zipCode);
  return parts.join(', ');
};

// Feature definitions
const FEATURES = [
  {
    id: 'stats',
    title: 'Statistics',
    icon: '📊',
    description: 'View performance stats, weekly counts, and recovery rates'
  },
  {
    id: 'vehicles',
    title: 'My Vehicles',
    icon: '🚗',
    description: 'Manage your vehicle list, add new vehicles, edit details'
  },
  {
    id: 'team-vehicles',
    title: 'Team Vehicles',
    icon: '👥',
    description: 'View and manage team member vehicles across the organization'
  },
  {
    id: 'chat',
    title: 'Team Chat',
    icon: '💬',
    description: 'Communicate with team members in real-time'
  },
  {
    id: 'trails',
    title: 'Location Trails',
    icon: '🛤️',
    description: 'View teammate location trails and GPS tracking'
  }
];

// Auto-detect drive type based on vehicle year, make, and model
const autoDetectDriveType = (vehicleName) => {
  if (!vehicleName || typeof vehicleName !== 'string') {
    return 'FWD'; // Default fallback
  }

  const name = vehicleName.toLowerCase().trim();
  
  // First, check for explicit drive type mentions in the name
  if (name.includes('awd') || name.includes('all wheel') || name.includes('all-wheel')) return 'AWD';
  if (name.includes('4wd') || name.includes('4x4') || name.includes('four wheel') || name.includes('4-wheel')) return '4WD';
  if (name.includes('rwd') || name.includes('rear wheel')) return 'RWD';
  if (name.includes('fwd') || name.includes('front wheel')) return 'FWD';
  
  // Extract year if present (4 digits)
  const yearMatch = name.match(/\b(19|20)\d{2}\b/);
  const year = yearMatch ? parseInt(yearMatch[0]) : null;
  
  // TRUCKS & LARGE SUVs - Often RWD with 4WD option
  if (name.includes('f-150') || name.includes('f150')) return name.includes('4wd') || name.includes('4x4') || name.includes('raptor') || name.includes('tremor') ? '4WD' : 'RWD';
  if (name.includes('f-250') || name.includes('f250')) return name.includes('4wd') || name.includes('4x4') ? '4WD' : 'RWD';
  if (name.includes('f-350') || name.includes('f350')) return name.includes('4wd') || name.includes('4x4') ? '4WD' : 'RWD';
  if (name.includes('silverado')) return name.includes('4wd') || name.includes('4x4') || name.includes('z71') || name.includes('trail boss') || name.includes('zr2') ? '4WD' : 'RWD';
  if (name.includes('sierra')) return name.includes('4wd') || name.includes('4x4') || name.includes('at4') ? '4WD' : 'RWD';
  if (name.includes('ram 1500') || name.includes('ram 2500') || name.includes('ram 3500')) return name.includes('4wd') || name.includes('4x4') || name.includes('rebel') || name.includes('trx') ? '4WD' : 'RWD';
  if (name.includes('tundra')) return name.includes('4wd') || name.includes('4x4') ? '4WD' : 'RWD';
  if (name.includes('titan')) return name.includes('4wd') || name.includes('4x4') ? '4WD' : 'RWD';
  if (name.includes('tacoma')) return name.includes('4wd') || name.includes('4x4') || name.includes('trd') ? '4WD' : 'RWD';
  if (name.includes('ranger')) return name.includes('4wd') || name.includes('4x4') || name.includes('raptor') ? '4WD' : 'RWD';
  if (name.includes('colorado')) return name.includes('4wd') || name.includes('4x4') || name.includes('z71') || name.includes('zr2') ? '4WD' : 'RWD';
  if (name.includes('canyon')) return name.includes('4wd') || name.includes('4x4') || name.includes('at4') ? '4WD' : 'RWD';
  if (name.includes('ridgeline')) return 'AWD';
  if (name.includes('frontier')) return name.includes('4wd') || name.includes('4x4') ? '4WD' : 'RWD';
  if (name.includes('gladiator')) return '4WD';
  if (name.includes('maverick')) return name.includes('awd') || name.includes('tremor') ? 'AWD' : 'FWD';
  
  // Default fallback
  return 'FWD';
};

// Get possible drive types info for a vehicle
const getDriveTypeInfo = (vehicleName) => {
  const name = vehicleName.toLowerCase().trim();
  
  // Extract vehicle make/model info
  let info = {
    detectedType: autoDetectDriveType(vehicleName),
    possibleTypes: [],
    subModels: []
  };
  
  // TRUCKS
  if (name.includes('f-150') || name.includes('f150') || name.includes('silverado') || name.includes('sierra') || name.includes('ram')) {
    info.possibleTypes = ['RWD', '4WD'];
    info.subModels = ['Base/Work Truck: RWD', 'Z71/4x4: 4WD', 'Raptor/TRX: 4WD'];
  }
  
  // Toyota/Honda SUVs
  else if (name.includes('rav4') || name.includes('cr-v') || name.includes('highlander')) {
    info.possibleTypes = ['FWD', 'AWD'];
    info.subModels = ['Base: FWD', 'AWD models: AWD'];
  }
  
  // Luxury Sedans
  else if (name.includes('bmw') && (name.includes('3') || name.includes('5'))) {
    info.possibleTypes = ['RWD', 'AWD'];
    info.subModels = ['328i/330i: RWD', '330xi/xDrive: AWD'];
  }
  
  // Subaru
  else if (name.includes('subaru')) {
    if (name.includes('brz')) {
      info.possibleTypes = ['RWD'];
      info.subModels = ['BRZ: RWD only'];
    } else {
      info.possibleTypes = ['AWD'];
      info.subModels = ['All models: AWD standard'];
    }
  }
  
  // Performance cars
  else if (name.includes('mustang') || name.includes('camaro') || name.includes('challenger')) {
    info.possibleTypes = ['RWD', 'AWD'];
    info.subModels = ['Base/GT: RWD', 'AWD (if available): AWD'];
  }
  
  // Default for most cars
  else {
    info.possibleTypes = [info.detectedType];
    info.subModels = [`Standard: ${info.detectedType}`];
  }
  
  return info;
};

function StandaloneVehicleTracker() {
  const [db, setDb] = useState(null);
  const [storage, setStorage] = useState(null);
  const [user, setUser] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [team, setTeam] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [initError, setInitError] = useState(null);

  // Vehicle tracking state
  const [vehicles, setVehicles] = useState([]);
  const [selectedWeek, setSelectedWeek] = useState(null);
  const [availableWeeks, setAvailableWeeks] = useState([]);
  const [vehicleStats, setVehicleStats] = useState({
    totalScans: 0,
    totalFound: 0,
    totalSecured: 0,
    recoveryRate: 0,
    dateRange: { start: '', end: '' }
  });

  // NEW: Vehicle selection state for map integration
  const [selectedVehicleId, setSelectedVehicleId] = useState(null);
  const [teamVehicles, setTeamVehicles] = useState([]);
  const [selectedVehicle, setSelectedVehicle] = useState(null);
  const [showVehicleDetail, setShowVehicleDetail] = useState(false);

  // NEW: Order management state
  const [orders, setOrders] = useState([]);
  const [selectedOrderId, setSelectedOrderId] = useState(null);
  const [ordersLoading, setOrdersLoading] = useState(false);

  // NEW: Features overlay state management - UPDATED to only allow one at a time
  const [showFeaturesSelector, setShowFeaturesSelector] = useState(false);
  const [activeFeature, setActiveFeature] = useState(null); // Only one active feature at a time
  
  // NEW: Personal vehicles map visibility toggle
  const [showPersonalVehiclesOnMap, setShowPersonalVehiclesOnMap] = useState(false);
  
  // NEW: Chat state
  const [chatMessages, setChatMessages] = useState([]);
  const [newChatMessage, setNewChatMessage] = useState('');
  const [chatLoading, setChatLoading] = useState(false);
  const [userProfilePictures, setUserProfilePictures] = useState(new Map());
  
  // NEW: Trails state
  const [showTrails, setShowTrails] = useState(false);
  const [trailTimeRange, setTrailTimeRange] = useState(TRAIL_TIME_RANGES[0]); // Default to 1H
  const [teammateLocations, setTeammateLocations] = useState(new Map());
  const [locationHistory, setLocationHistory] = useState(new Map());
  const [teammateColors, setTeammateColors] = useState(new Map());
  const [allTeamMembers, setAllTeamMembers] = useState(new Set());
  const [trailsLoading, setTrailsLoading] = useState(false);
  
  // Enhanced form state with VIN verification, images, color, and DO NOT SECURE
  const [showAddForm, setShowAddForm] = useState(false);
  const [showDoNotSecureReason, setShowDoNotSecureReason] = useState(false);
  const [doNotSecureReason, setDoNotSecureReason] = useState('');
  const [customDoNotSecureReason, setCustomDoNotSecureReason] = useState('');
  
  // Load saved form data from localStorage on mount
  const getInitialFormData = () => {
    const saved = localStorage.getItem('vehicleFormDraft');
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        // Ensure date is current if it's old
        if (parsed.date && new Date(parsed.date) < new Date(new Date().setHours(0,0,0,0))) {
          parsed.date = new Date().toISOString().split('T')[0];
        }
        return parsed;
      } catch (e) {
        console.error('Error loading saved form:', e);
      }
    }
    return {
      vehicle: '',
      vin: '',
      vinVerified: false,
      status: 'PENDING PICKUP',
      date: new Date().toISOString().split('T')[0],
      plateNumber: '',
      accountNumber: '',
      financier: '',
      address: '',
      city: '',        // NEW FIELD
      state: '',       // NEW FIELD
      zipCode: '',     // NEW FIELD
      position: null,
      notes: '',
      images: [],
      color: '',
      driveType: '',
      doNotSecureReason: ''
    };
  };
  
  const [newVehicle, setNewVehicle] = useState(getInitialFormData());

  // Geolocation state
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [locationError, setLocationError] = useState(null);

  // Image upload state
  const [uploadingImages, setUploadingImages] = useState(false);
  const [imageUploadProgress, setImageUploadProgress] = useState(0);
  const fileInputRef = useRef(null);
  const editFileInputRef = useRef(null);
  const cameraInputRef = useRef(null);
  const editCameraInputRef = useRef(null);

  // Image modal state
  const [selectedImage, setSelectedImage] = useState(null);
  const [showImageModal, setShowImageModal] = useState(false);

  // Enhanced editing state with color and drive type
  const [editingVehicle, setEditingVehicle] = useState(null);
  const [editVehicleData, setEditVehicleData] = useState({});
  const [editUploadingImages, setEditUploadingImages] = useState(false);
  const [editImageUploadProgress, setEditImageUploadProgress] = useState(0);
  const [isGettingEditLocation, setIsGettingEditLocation] = useState(false);

  // Notes editing state
  const [editingNotes, setEditingNotes] = useState(null);
  const [tempNotes, setTempNotes] = useState('');

  // Scan editing state
  const [editingScans, setEditingScans] = useState(false);
  const [editScanAmount, setEditScanAmount] = useState(0);

  // YTD and statistics
  const [lifetimeSecuredCount, setLifetimeSecuredCount] = useState(0);
  const [ytdStats, setYtdStats] = useState({
    totalFound: 0,
    totalSecured: 0,
    totalScans: 0,
    recoveryRate: 0,
    month: {
      totalFound: 0,
      totalSecured: 0,
      totalScans: 0,
      recoveryRate: 0
    }
  });

  // Team sync notifications
  const [teamSyncNotifications, setTeamSyncNotifications] = useState([]);

  // Sound notification settings
  const [soundEnabled, setSoundEnabled] = useState(true);
  const notificationSoundRef = useRef(null);
  const moneySoundRef = useRef(null);

  // Print functionality
  const [showPrintView, setShowPrintView] = useState(false);
  const printRef = useRef(null);

  // Viewport width detection for responsive design
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [showActions, setShowActions] = useState({});

  // User permissions
  const [canSecureVehicles, setCanSecureVehicles] = useState(false);

  // NEW: Feature toggle functions - UPDATED to only allow one at a time
  const toggleFeature = (featureId) => {
    if (activeFeature === featureId) {
      // If clicking the same feature, close it
      setActiveFeature(null);
    } else {
      // Open the new feature, closing any existing one
      setActiveFeature(featureId);
    }
  };

  const isFeatureOpen = (featureId) => activeFeature === featureId;

  // NEW: Chat functions
  const loadChatMessages = useCallback(async () => {
    if (!db || !team || !user) return;

    try {
      setChatLoading(true);
      console.log('📬 Loading chat messages for team:', team.id);

      const chatQuery = query(
        collection(db, 'teamChat'),
        where('teamId', '==', team.id),
        orderBy('timestamp', 'desc'),
        limit(50)
      );

      const unsubscribe = onSnapshot(chatQuery, (snapshot) => {
        const messages = [];
        snapshot.docs.forEach(doc => {
          const data = doc.data();
          messages.push({
            id: doc.id,
            ...data,
            timestamp: data.timestamp?.toDate() || new Date()
          });
        });

        // Reverse to show oldest first
        setChatMessages(messages.reverse());
        setChatLoading(false);
      }, (error) => {
        console.error('❌ Error loading chat messages:', error);
        setChatMessages([]);
        setChatLoading(false);
      });

      return unsubscribe;
    } catch (error) {
      console.error('❌ Error setting up chat listener:', error);
      setChatMessages([]);
      setChatLoading(false);
    }
  }, [db, team, user]);

  const sendChatMessage = useCallback(async () => {
    if (!newChatMessage.trim() || !db || !team || !user) return;

    try {
      const messageData = {
        teamId: team.id,
        userId: user.id,
        userName: user.displayName || user.email?.split('@')[0] || 'User',
        message: newChatMessage.trim(),
        timestamp: serverTimestamp(),
        createdAt: new Date()
      };

      await addDoc(collection(db, 'teamChat'), messageData);
      setNewChatMessage('');
      
      // Play notification sound
      playNotificationSound(soundEnabled);
      
    } catch (error) {
      console.error('❌ Error sending chat message:', error);
      alert('Failed to send message. Please try again.');
    }
  }, [newChatMessage, db, team, user, soundEnabled]);

  // NEW: Trails functions
  const loadTeamLocations = useCallback(async () => {
    if (!db || !team || !user) return;

    try {
      setTrailsLoading(true);
      console.log('🛤️ Loading team locations for trails...');

      // Get recent locations for current positions
      const recentLocationsQuery = query(
        collection(db, 'teamLocations'),
        where('teamId', '==', team.id),
        orderBy('timestamp', 'desc'),
        limit(100)
      );

      const unsubscribe = onSnapshot(recentLocationsQuery, (snapshot) => {
        const locations = new Map();
        const history = new Map();
        const members = new Set();
        const colors = new Map();

        let colorIndex = 0;
        const teamMemberColors = [
          '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57', '#FF9FF3', 
          '#54A0FF', '#5F27CD', '#00D2D3', '#FF9F43', '#8395A7', '#FD79A8', 
          '#6C5CE7', '#A29BFE', '#FD8D07', '#26DE81', '#FC427B', '#0FB9B1', 
          '#F7B731', '#5D2AFF'
        ];

        snapshot.docs.forEach(doc => {
          const data = doc.data();
          const userId = data.userId;
          const locationData = {
            ...data,
            timestamp: data.timestamp?.toDate() || new Date(),
            id: doc.id
          };

          members.add(userId);

          // Assign color if not already assigned
          if (!colors.has(userId)) {
            colors.set(userId, teamMemberColors[colorIndex % teamMemberColors.length]);
            colorIndex++;
          }

          // Keep most recent location for each user
          if (!locations.has(userId) || locationData.timestamp > locations.get(userId).timestamp) {
            locations.set(userId, locationData);
          }

          // Add to history
          if (!history.has(userId)) {
            history.set(userId, []);
          }
          history.get(userId).push(locationData);
        });

        // Sort history by timestamp for each user
        history.forEach((userHistory, userId) => {
          userHistory.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
        });

        setTeammateLocations(locations);
        setLocationHistory(history);
        setAllTeamMembers(members);
        setTeammateColors(colors);
        setTrailsLoading(false);
      }, (error) => {
        console.error('❌ Error loading team locations:', error);
        setTrailsLoading(false);
      });

      return unsubscribe;
    } catch (error) {
      console.error('❌ Error setting up locations listener:', error);
      setTrailsLoading(false);
    }
  }, [db, team, user]);

  const handleTrailTimeRangeChange = (range) => {
    setTrailTimeRange(range);
  };

  const toggleTrails = () => {
    setShowTrails(!showTrails);
  };

  const deleteUserTrails = async (userId) => {
    if (!db || !team || !confirm('Are you sure you want to delete this user\'s trails?')) return;

    try {
      const batch = writeBatch(db);
      const locationsQuery = query(
        collection(db, 'teamLocations'),
        where('teamId', '==', team.id),
        where('userId', '==', userId)
      );

      const snapshot = await getDocs(locationsQuery);
      snapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });

      await batch.commit();
      alert('User trails deleted successfully!');
    } catch (error) {
      console.error('Error deleting user trails:', error);
      alert('Failed to delete user trails.');
    }
  };

  const deleteAllTrails = async () => {
    if (!db || !team || !confirm('Are you sure you want to delete ALL team trails? This cannot be undone!')) return;

    try {
      const batch = writeBatch(db);
      const locationsQuery = query(
        collection(db, 'teamLocations'),
        where('teamId', '==', team.id)
      );

      const snapshot = await getDocs(locationsQuery);
      snapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });

      await batch.commit();
      alert('All team trails deleted successfully!');
    } catch (error) {
      console.error('Error deleting all trails:', error);
      alert('Failed to delete trails.');
    }
  };

  // Check if current user is admin
  const isAdmin = useMemo(() => {
    if (!userProfile || !user) return false;
    
    // Check for admin role
    const role = (userProfile.role || '').toLowerCase();
    if (role.includes('admin') || role.includes('manager') || role.includes('supervisor')) {
      return true;
    }
    
    // Check for admin permissions
    if (userProfile.isAdmin === true || userProfile.admin === true) {
      return true;
    }
    
    // Check team ownership or management
    if (team && (team.ownerId === user.id || team.managerId === user.id)) {
      return true;
    }
    
    return false;
  }, [userProfile, user, team]);

  // NEW: Load team vehicles function (moved from TeamVehicleTracker)
  const loadTeamVehicles = useCallback(async () => {
    if (!db || !team || !user) return;

    try {
      console.log('🔄 Loading team vehicles for map display...');
      
      const teamMemberIds = new Set();
      
      // Get team members from various sources
      if (team.teamMembers && Array.isArray(team.teamMembers)) {
        team.teamMembers.forEach(id => teamMemberIds.add(id));
      }
      
      // Try subcollection
      try {
        const membersCollection = collection(db, 'teams', team.id, 'teamMembers');
        const membersSnapshot = await getDocs(membersCollection);
        membersSnapshot.docs.forEach(doc => {
          const data = doc.data();
          if (data.userId) teamMemberIds.add(data.userId);
        });
      } catch (e) {
        console.log('No team members subcollection');
      }
      
      // Add current user
      teamMemberIds.add(user.id);
      
      // Try to find users by team reference
      try {
        const usersQuery = query(
          collection(db, 'users'),
          where('team', '==', team.id)
        );
        const usersSnapshot = await getDocs(usersQuery);
        usersSnapshot.docs.forEach(doc => teamMemberIds.add(doc.id));
      } catch (e) {
        console.log('Could not query users by team');
      }

      console.log(`👥 Found ${teamMemberIds.size} team members`);
      
      const allTeamVehicles = [];
      // FIXED: Use selectedWeek if available, otherwise use current week
      const weekId = selectedWeek || getCurrentWeekId();
      console.log(`📅 Loading team vehicles for week: ${weekId}`);
      
      // Load vehicles for all team members
      for (const memberId of teamMemberIds) {
        try {
          const memberVehiclesRef = collection(db, 'users', memberId, 'vehicleWeeks', weekId, 'vehicles');
          const memberSnapshot = await getDocs(memberVehiclesRef);
          
          // Get user profile for display name
          let memberProfile = null;
          try {
            const profileDoc = await getDoc(doc(db, 'userProfiles', memberId));
            if (profileDoc.exists()) {
              memberProfile = profileDoc.data();
            }
          } catch (e) {
            console.log(`Could not load profile for ${memberId}`);
          }
          
          const memberDisplayName = memberProfile?.displayName || 
                                   memberProfile?.firstName || 
                                   (memberId === user.id ? user.displayName : null) ||
                                   `User ${memberId.substring(0, 8)}`;
          
          memberSnapshot.docs.forEach(doc => {
            const vehicleData = doc.data();
            const uniqueKey = `${memberId}_${weekId}_${doc.id}`;
            
            allTeamVehicles.push({
              id: doc.id,
              uniqueKey: uniqueKey,
              teamMemberId: memberId,
              teamMemberName: memberDisplayName,
              weekId: weekId,
              weekRange: availableWeeks.find(w => w.id === weekId)?.displayRange || weekId,
              isOwnVehicle: memberId === user.id,
              ...vehicleData
            });
          });
          
        } catch (error) {
          console.error(`Error loading vehicles for member ${memberId}:`, error);
        }
      }
      
      console.log(`🚗 Loaded ${allTeamVehicles.length} total team vehicles`);
      setTeamVehicles(allTeamVehicles);
      
    } catch (error) {
      console.error('Error loading team vehicles:', error);
      setTeamVehicles([]);
    }
  }, [db, team, user, selectedWeek]);

  // NEW: Vehicle selection callback - called when vehicle is selected from map or team tracker
  const handleVehicleSelect = useCallback((vehicleUniqueKey) => {
    console.log('🎯 Vehicle selected:', vehicleUniqueKey);
    setSelectedVehicleId(vehicleUniqueKey);
    
    // Find the selected vehicle and show detail card
    const vehicle = teamVehicles.find(v => v.uniqueKey === vehicleUniqueKey);
    if (vehicle) {
      setSelectedVehicle(vehicle);
      setShowVehicleDetail(true);
    }
  }, [teamVehicles]);

  // NEW: Order selection callback
  const handleOrderSelect = useCallback((orderId) => {
    console.log('📋 Order selected:', orderId);
    setSelectedOrderId(orderId);
  }, []);

  // NEW: Order navigation callback
  const handleOrderNavigate = useCallback((order) => {
    console.log('🗺️ Navigating to order:', order.id);
    if (order.position || (order.addresses && order.addresses.length > 0)) {
      const address = order.addresses?.[0];
      let coords = order.position;
      
      if (!coords && address?.position) {
        coords = address.position;
      }
      
      if (coords) {
        // Open navigation in default maps app
        const url = `https://www.google.com/maps/dir/?api=1&destination=${coords.lat},${coords.lng}`;
        window.open(url, '_blank');
      } else {
        // Fallback to address string
        const addressString = typeof address === 'string' ? address : 
          `${address?.street || ''} ${address?.city || ''} ${address?.state || ''} ${address?.zip || ''}`.trim();
        if (addressString) {
          const url = `https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(addressString)}`;
          window.open(url, '_blank');
        }
      }
    }
  }, []);

  // NEW: Order secure callback
  const handleOrderSecure = useCallback(async (order) => {
    console.log('🔒 Securing order:', order.id);
    if (!db || !user) return;

    try {
      // Update the order status to secured
      await updateDoc(doc(db, 'orders', order.id), {
        status: 'secure',
        secure: true,
        securedBy: user.displayName || user.email,
        secureTimestamp: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      // Also update in locations collection if it exists
      const locationRef = doc(db, 'locations', order.id);
      const locationDoc = await getDoc(locationRef);
      if (locationDoc.exists()) {
        await updateDoc(locationRef, {
          status: 'secured',
          updatedAt: serverTimestamp()
        });
      }

      // Show success message
      alert(`Successfully secured order: ${order.year} ${order.make} ${order.model}`);
      
      // Play money sound
      playMoneySound(soundEnabled);
      
      // Reload orders
      loadOrders();
    } catch (error) {
      console.error('Error securing order:', error);
      alert('Failed to secure order. Please try again.');
    }
  }, [db, user, soundEnabled]);

  // NEW: Load orders from Firestore
  const loadOrders = useCallback(async () => {
    if (!db || !team) return;

    try {
      setOrdersLoading(true);
      console.log('📋 Loading orders for team:', team.id);

      // Query for open orders only (to reduce map clutter)
      const ordersQuery = query(
        collection(db, 'orders'),
        where('teamId', '==', team.id),
        where('status', 'in', ['open', 'pending', 'open-order', 'pending-pickup']),
        orderBy('createdAt', 'desc')
      );

      const unsubscribe = onSnapshot(ordersQuery, (snapshot) => {
        const ordersData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        console.log(`📋 Loaded ${ordersData.length} open orders for map display`);
        setOrders(ordersData);
        setOrdersLoading(false);
      }, (error) => {
        console.error('❌ Error loading orders:', error);
        setOrders([]);
        setOrdersLoading(false);
      });

      return unsubscribe;
    } catch (error) {
      console.error('❌ Error setting up orders listener:', error);
      setOrders([]);
      setOrdersLoading(false);
    }
  }, [db, team]);

  // Handle vehicle deleted from map
  const handleVehicleDeletedFromMap = (deletedVehicle) => {
    console.log('🗑️ Vehicle deleted from map:', deletedVehicle.vehicle);
    
    // Remove from team vehicles state
    setTeamVehicles(prev => prev.filter(v => 
      (v.uniqueKey || v.id) !== (deletedVehicle.uniqueKey || deletedVehicle.id)
    ));
    
    // Show notification
    const notification = {
      id: Date.now(),
      type: 'admin_delete',
      message: `🗑️ Admin moved ${deletedVehicle.vehicle} (VIN: ${deletedVehicle.vin}) to Never Secured`,
      timestamp: new Date()
    };

    setTeamSyncNotifications(prev => [notification, ...prev.slice(0, 4)]);
    setTimeout(() => {
      setTeamSyncNotifications(prev => prev.filter(n => n.id !== notification.id));
    }, 8000);
  };

  // Initialize PDF libraries on component mount
  useEffect(() => {
    const timer = setTimeout(() => {
      loadPDFLibraries();
    }, 1000);
    
    return () => clearTimeout(timer);
  }, []);

  // Suppress Google API errors on mobile
  useEffect(() => {
    suppressGoogleAPIErrors();
  }, []);

  // Reverse geocoding function
  const reverseGeocodeCoordinates = async (lat, lng) => {
    try {
      console.log(`Reverse geocoding coordinates: ${lat}, ${lng}`);
      
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&addressdetails=1`,
        {
          headers: {
            'User-Agent': 'VehicleTracker/1.0'
          }
        }
      );
      
      if (!response.ok) {
        throw new Error(`Reverse geocoding API error: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data && data.address) {
        const addr = data.address;
        let streetAddress = '';
        let city = '';
        let state = '';
        let zipCode = '';
        
        // Build street address
        if (addr.house_number) streetAddress += addr.house_number + ' ';
        if (addr.road) streetAddress += addr.road;
        else if (addr.street) streetAddress += addr.street;
        
        // Get city
        city = addr.city || addr.town || addr.village || addr.suburb || '';
        
        // Get state
        state = addr.state || '';
        
        // Get zip code
        zipCode = addr.postcode || '';
        
        return {
          streetAddress: streetAddress.trim(),
          city: city,
          state: state,
          zipCode: zipCode,
          fullAddress: data.display_name
        };
      }
      
      return null;
    } catch (error) {
      console.error('Reverse geocoding error:', error);
      return null;
    }
  };

  // Get current location and address for new vehicle
  const getCurrentLocationAndAddress = async () => {
    if (!navigator.geolocation) {
      alert('Geolocation is not supported by your browser');
      return;
    }
    
    setIsGettingLocation(true);
    setLocationError(null);
    
    try {
      const position = await new Promise((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          (position) => resolve(position),
          (error) => reject(error),
          {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 0
          }
        );
      });
      
      const { latitude, longitude } = position.coords;
      const coordinates = { lat: latitude, lng: longitude };
      
      console.log('Got current location:', coordinates);
      
      // Get the address components
      const addressData = await reverseGeocodeCoordinates(latitude, longitude);
      
      if (addressData) {
        setNewVehicle(prev => {
          const updated = {
            ...prev,
            position: coordinates,
            address: addressData.streetAddress,
            city: addressData.city,
            state: addressData.state,
            zipCode: addressData.zipCode
          };
          // Auto-save to localStorage
          localStorage.setItem('vehicleFormDraft', JSON.stringify(updated));
          return updated;
        });
        
        console.log('Location and address captured:', { coordinates, addressData });
        playNotificationSound(soundEnabled);
        
      } else {
        setNewVehicle(prev => {
          const updated = {
            ...prev,
            position: coordinates,
            address: '',
            city: '',
            state: '',
            zipCode: ''
          };
          // Auto-save to localStorage
          localStorage.setItem('vehicleFormDraft', JSON.stringify(updated));
          return updated;
        });
        alert('Location captured but could not determine address. You can enter it manually.');
      }
    } catch (error) {
      console.error('Error getting location:', error);
      
      let errorMessage = 'Failed to get your location. ';
      if (error.code === 1) {
        errorMessage += 'Please allow location access and try again.';
      } else if (error.code === 2) {
        errorMessage += 'Location information is unavailable.';
      } else if (error.code === 3) {
        errorMessage += 'Location request timed out.';
      } else {
        errorMessage += error.message || 'Please try again.';
      }
      
      setLocationError(errorMessage);
      alert(errorMessage);
    } finally {
      setIsGettingLocation(false);
    }
  };

  // Get current location and address for edit vehicle
  const getCurrentLocationAndAddressForEdit = async () => {
    if (!navigator.geolocation) {
      alert('Geolocation is not supported by your browser');
      return;
    }
    
    setIsGettingEditLocation(true);
    
    try {
      const position = await new Promise((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          (position) => resolve(position),
          (error) => reject(error),
          {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 0
          }
        );
      });
      
      const { latitude, longitude } = position.coords;
      const coordinates = { lat: latitude, lng: longitude };
      
      console.log('Got current location for edit:', coordinates);
      
      // Get the address components
      const addressData = await reverseGeocodeCoordinates(latitude, longitude);
      
      if (addressData) {
        setEditVehicleData(prev => ({
          ...prev,
          position: coordinates,
          address: addressData.streetAddress,
          city: addressData.city,
          state: addressData.state,
          zipCode: addressData.zipCode
        }));
        
        console.log('Edit location and address captured:', { coordinates, addressData });
        playNotificationSound(soundEnabled);
        
      } else {
        setEditVehicleData(prev => ({
          ...prev,
          position: coordinates
        }));
        alert('Location captured but could not determine address. You can enter it manually.');
      }
    } catch (error) {
      console.error('Error getting location for edit:', error);
      
      let errorMessage = 'Failed to get your location. ';
      if (error.code === 1) {
        errorMessage += 'Please allow location access and try again.';
      } else if (error.code === 2) {
        errorMessage += 'Location information is unavailable.';
      } else if (error.code === 3) {
        errorMessage += 'Location request timed out.';
      } else {
        errorMessage += error.message || 'Please try again.';
      }
      
      alert(errorMessage);
    } finally {
      setIsGettingEditLocation(false);
    }
  };

  // AGGRESSIVE cleanup function to remove incorrectly added team vehicles from ALL weeks
  const cleanupIncorrectlyAddedVehicles = async () => {
    if (!db || !user) return;

    try {
      console.log('🧹 Starting aggressive cleanup of incorrectly added team vehicles...');
      
      // Get ALL weeks for this user
      const weeksQuery = query(
        collection(db, 'users', user.id, 'vehicleWeeks'),
        orderBy('startDate', 'desc')
      );
      const weeksSnapshot = await getDocs(weeksQuery);
      
      let totalRemovedCount = 0;
      const vinsSeen = new Set(); // Track VINs to prevent duplicates
      
      // Go through EVERY week
      for (const weekDoc of weeksSnapshot.docs) {
        const weekId = weekDoc.id;
        console.log(`Checking week: ${weekId}`);
        
        const vehiclesRef = collection(db, 'users', user.id, 'vehicleWeeks', weekId, 'vehicles');
        const snapshot = await getDocs(vehiclesRef);
        
        let weekRemovedCount = 0;
        
        for (const doc of snapshot.docs) {
          const vehicle = doc.data();
          
          // Remove vehicles that:
          // 1. Have teamMemberId that's not the current user
          // 2. Have copiedFromTeammate flag
          // 3. Have fromTeammate flag (old bottom status vehicles)
          // 4. Have bottomStatus but are not secured
          // 5. Are not secured by this user (no securedFromTeammate flag)
          // 6. Have duplicate VINs (keep only the oldest one)
          const shouldRemove = (
            (vehicle.teamMemberId && vehicle.teamMemberId !== user.id && !vehicle.securedFromTeammate) ||
            vehicle.copiedFromTeammate === true ||
            (vehicle.fromTeammate === true && vehicle.status !== 'SECURED') ||
            (vehicle.bottomStatus && vehicle.status !== 'SECURED' && !vehicle.addedBy?.includes(user.email)) ||
            (vehicle.vin && vinsSeen.has(vehicle.vin.toUpperCase()) && vehicle.status !== 'SECURED')
          );
          
          if (shouldRemove) {
            console.log(`Removing vehicle from week ${weekId}: ${vehicle.vehicle} (VIN: ${vehicle.vin})`);
            console.log(`  Reason: teamMemberId=${vehicle.teamMemberId}, copiedFromTeammate=${vehicle.copiedFromTeammate}, fromTeammate=${vehicle.fromTeammate}, bottomStatus=${vehicle.bottomStatus}`);
            await deleteDoc(doc.ref);
            weekRemovedCount++;
          } else if (vehicle.vin) {
            // Track this VIN so we can remove duplicates
            vinsSeen.add(vehicle.vin.toUpperCase());
          }
        }
        
        if (weekRemovedCount > 0) {
          console.log(`✅ Removed ${weekRemovedCount} vehicles from week ${weekId}`);
          totalRemovedCount += weekRemovedCount;
        }
      }
      
      if (totalRemovedCount > 0) {
        console.log(`✅ Total removed: ${totalRemovedCount} incorrectly added vehicles across all weeks`);
        alert(`Cleaned up ${totalRemovedCount} vehicles that were incorrectly added to your list across all weeks.`);
        
        // Reload the current week data
        await loadVehicleDataForWeek(selectedWeek);
      } else {
        console.log('✅ No incorrectly added vehicles found');
        alert('No incorrectly added vehicles found in any week.');
      }
      
    } catch (error) {
      console.error('Error cleaning up vehicles:', error);
      alert('Error during cleanup. Check console for details.');
    }
  };

  // FIXED: Enhanced carryover logic to prevent duplicates
  const carryOverFoundVehicles = async (fromWeekId, toWeekId) => {
    if (!db || !user) return;

    try {
      console.log(`🔄 Carrying over vehicles from ${fromWeekId} to ${toWeekId}`);

      // Get all vehicles from the previous week that are not SECURED
      const prevWeekVehiclesRef = collection(db, 'users', user.id, 'vehicleWeeks', fromWeekId, 'vehicles');
      const prevWeekSnapshot = await getDocs(prevWeekVehiclesRef);
      
      // Get current week vehicles to avoid duplicates
      const currentWeekVehiclesRef = collection(db, 'users', user.id, 'vehicleWeeks', toWeekId, 'vehicles');
      const currentWeekSnapshot = await getDocs(currentWeekVehiclesRef);
      
      // Create a set of VINs already in current week - CASE INSENSITIVE
      const currentWeekVINs = new Set();
      currentWeekSnapshot.docs.forEach(doc => {
        const data = doc.data();
        if (data.vin && data.vin.trim() !== '') {
          currentWeekVINs.add(data.vin.trim().toUpperCase());
        }
      });
      
      console.log(`📋 Current week has ${currentWeekVINs.size} vehicles with VINs`);
      
      const vehiclesToCarryOver = [];
      
      prevWeekSnapshot.docs.forEach(docSnap => {
        const vehicleData = docSnap.data();
        
        // Only carry over if:
        // 1. Not secured
        // 2. Has a VIN
        // 3. VIN is not already in current week (case insensitive check)
        // 4. Is user's own vehicle (not a team vehicle that was incorrectly added)
        const isOwnVehicle = !vehicleData.teamMemberId || vehicleData.teamMemberId === user.id || vehicleData.securedFromTeammate === true;
        
        if (vehicleData.status !== 'SECURED' && 
            vehicleData.vin && 
            vehicleData.vin.trim() !== '' &&
            !currentWeekVINs.has(vehicleData.vin.trim().toUpperCase()) &&
            isOwnVehicle &&
            !vehicleData.copiedFromTeammate &&
            !vehicleData.fromTeammate &&
            !vehicleData.bottomStatus) {
          vehiclesToCarryOver.push({
            ...vehicleData,
            id: docSnap.id
          });
        }
      });

      console.log(`📦 Found ${vehiclesToCarryOver.length} vehicles to carry over`);
      vehiclesToCarryOver.forEach(v => {
        console.log(`  - ${v.vehicle} (${v.vin}) - Status: ${v.status}`);
      });

      // Add each vehicle to the new week as a carryover
      for (const vehicle of vehiclesToCarryOver) {
        // Clean the vehicle data before carrying over
        const carryoverVehicleData = {
          vehicle: vehicle.vehicle,
          vin: vehicle.vin,
          vinVerified: vehicle.vinVerified || false,
          status: vehicle.status,
          date: vehicle.date,
          plateNumber: vehicle.plateNumber || '',
          accountNumber: vehicle.accountNumber || '',
          financier: vehicle.financier || '',
          address: vehicle.address || '',
          city: vehicle.city || '',
          state: vehicle.state || '',
          zipCode: vehicle.zipCode || '',
          position: vehicle.position || null,
          notes: vehicle.notes || '',
          images: vehicle.images || [],
          color: vehicle.color || '',
          driveType: vehicle.driveType || '',
          doNotSecureReason: vehicle.doNotSecureReason || '',
          carriedOver: true,
          originalWeekId: vehicle.originalWeekId || fromWeekId,
          originalId: vehicle.originalId || vehicle.id,
          carriedOverDate: new Date().toISOString().split('T')[0],
          lastCarriedFromWeek: fromWeekId,
          carryoverCount: (vehicle.carryoverCount || 0) + 1,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          addedBy: user.displayName || user.email,
          addedByUserId: user.id
        };

        // Explicitly remove any team-related fields
        delete carryoverVehicleData.teamMemberId;
        delete carryoverVehicleData.teamMemberName;
        delete carryoverVehicleData.copiedFromTeammate;
        delete carryoverVehicleData.fromTeammate;
        delete carryoverVehicleData.bottomStatus;
        delete carryoverVehicleData.bottomStatusCount;
        delete carryoverVehicleData.bottomStatusByUserId;
        delete carryoverVehicleData.bottomStatusByUserName;
        delete carryoverVehicleData.bottomStatusDate;

        await addDoc(currentWeekVehiclesRef, carryoverVehicleData);
        console.log(`✅ Carried over vehicle: ${vehicle.vehicle} (${vehicle.vin})`);
      }

    } catch (error) {
      console.error("❌ Error carrying over vehicles:", error);
    }
  };

  // Manual check for missing carryovers - FIXED
  const checkForMissingCarryovers = async () => {
    if (!db || !user || !selectedWeek || !availableWeeks) return;
    
    try {
      setLoading(true);
      
      // Find the previous week
      const currentWeekIndex = availableWeeks.findIndex(w => w.id === selectedWeek);
      const previousWeek = availableWeeks[currentWeekIndex + 1]; // Since weeks are sorted desc
      
      if (!previousWeek) {
        alert('No previous week found to check for carryovers.');
        return;
      }
      
      console.log(`🔍 Manually checking for carryovers from ${previousWeek.id} to ${selectedWeek}`);
      
      // Get all vehicles from previous week
      const prevWeekVehiclesRef = collection(db, 'users', user.id, 'vehicleWeeks', previousWeek.id, 'vehicles');
      const prevWeekSnapshot = await getDocs(prevWeekVehiclesRef);
      
      // Get current week vehicles to check what's already there
      const currentWeekVehiclesRef = collection(db, 'users', user.id, 'vehicleWeeks', selectedWeek, 'vehicles');
      const currentWeekSnapshot = await getDocs(currentWeekVehiclesRef);
      
      // Create a set of VINs already in current week - CASE INSENSITIVE
      const currentWeekVINs = new Set();
      currentWeekSnapshot.docs.forEach(doc => {
        const data = doc.data();
        if (data.vin && data.vin.trim() !== '') {
          currentWeekVINs.add(data.vin.trim().toUpperCase());
        }
      });
      
      // Find vehicles that should be carried over
      const vehiclesToCarryOver = [];
      prevWeekSnapshot.docs.forEach(docSnap => {
        const vehicleData = docSnap.data();
        
        // Check if this vehicle should be carried over
        if (vehicleData.status !== 'SECURED' && 
            vehicleData.vin && 
            vehicleData.vin.trim() !== '' &&
            !currentWeekVINs.has(vehicleData.vin.trim().toUpperCase())) {
          vehiclesToCarryOver.push({
            ...vehicleData,
            id: docSnap.id
          });
        }
      });
      
      if (vehiclesToCarryOver.length === 0) {
        alert('No missing vehicles found. All pending vehicles have been carried over.');
        return;
      }
      
      const confirmMessage = `Found ${vehiclesToCarryOver.length} missing vehicle(s) from previous week:\n\n` +
        vehiclesToCarryOver.map(v => `- ${v.vehicle} (VIN: ${v.vin}) - ${v.status}`).join('\n') +
        '\n\nDo you want to carry them over to this week?';
      
      if (confirm(confirmMessage)) {
        // Carry over the missing vehicles
        for (const vehicle of vehiclesToCarryOver) {
          const carryoverVehicleData = {
            ...vehicle,
            carriedOver: true,
            originalWeekId: vehicle.originalWeekId || previousWeek.id,
            originalId: vehicle.originalId || vehicle.id,
            carriedOverDate: new Date().toISOString().split('T')[0],
            lastCarriedFromWeek: previousWeek.id,
            carryoverCount: (vehicle.carryoverCount || 0) + 1,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp()
          };
          
          delete carryoverVehicleData.id;
          
          await addDoc(currentWeekVehiclesRef, carryoverVehicleData);
          console.log(`✅ Manually carried over: ${vehicle.vehicle} (${vehicle.vin})`);
        }
        
        alert(`Successfully carried over ${vehiclesToCarryOver.length} vehicle(s) to this week.`);
        
        // Reload the current week data
        await loadVehicleDataForWeek(selectedWeek);
      }
      
    } catch (error) {
      console.error('Error checking for missing carryovers:', error);
      alert('Error checking for missing carryovers. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Get user display name
  const getUserDisplayName = () => {
    if (!user) return "";

    if (user.displayName) {
      return user.displayName.toUpperCase();
    } else if (user.email) {
      return user.email.split('@')[0].toUpperCase();
    }
    return "USER";
  };

  // Get week display name
  const getWeekDisplayName = () => {
    if (!selectedWeek || !availableWeeks) return "CURRENT WEEK";

    const week = availableWeeks.find(w => w.id === selectedWeek);
    return week ? (week.displayRange || "WEEK") : "CURRENT WEEK";
  };

  // Show team sync notification with sound
  const showTeamSyncNotification = (vehicleData) => {
    if (vehicleData.autoSecuredFromTeam && vehicleData.securedByUserName) {
      const notification = {
        id: Date.now(),
        type: 'team_sync',
        message: `🎉 ${vehicleData.securedByUserName} secured ${vehicleData.vehicle} (VIN: ${vehicleData.vin})`,
        timestamp: new Date(),
        vehicleId: vehicleData.id
      };

      playNotificationSound(soundEnabled);

      setTeamSyncNotifications(prev => [notification, ...prev.slice(0, 4)]);

      setTimeout(() => {
        setTeamSyncNotifications(prev => prev.filter(n => n.id !== notification.id));
      }, 10000);
    }
  };

  // ENHANCED: Load available weeks with automatic carryover
  const loadAvailableWeeks = async () => {
    if (!db || !user) return;

    try {
      setVehicles([]);
      setVehicleStats({
        totalScans: 0,
        totalFound: 0,
        totalSecured: 0,
        recoveryRate: 0,
        dateRange: { start: '', end: '' }
      });

      const weeksQuery = query(
        collection(db, 'users', user.id, 'vehicleWeeks'),
        orderBy('endDate', 'desc')
      );

      const weeksSnapshot = await getDocs(weeksQuery);
      const weeksData = weeksSnapshot.docs.map(doc => {
        const data = doc.data();
        const startDate = data.startDate?.toDate();
        const endDate = data.endDate?.toDate();

        let displayRange = data.displayRange;
        if (startDate && endDate && (!displayRange || displayRange.trim() === '')) {
          displayRange = formatDateRange(startDate, endDate);
        }

        return {
          id: doc.id,
          ...data,
          startDate: startDate,
          endDate: endDate,
          displayRange: displayRange
        };
      });

      setAvailableWeeks(weeksData);

      const today = new Date();
      let currentWeek = weeksData.find(week =>
        week.startDate <= today && week.endDate >= today
      );

      const mostRecentWeek = weeksData.length > 0 ? weeksData[0] : null;

      let weekToSelect = null;
      if (currentWeek) {
        weekToSelect = currentWeek;
      } else if (mostRecentWeek) {
        weekToSelect = mostRecentWeek;
      } else {
        const currentWeekId = getCurrentWeekId();
        await createWeekIfNeeded(db, user.id, currentWeekId);

        const newWeekDoc = await getDoc(doc(db, 'users', user.id, 'vehicleWeeks', currentWeekId));
        if (newWeekDoc.exists()) {
          const newWeekData = newWeekDoc.data();
          weekToSelect = {
            id: currentWeekId,
            ...newWeekData,
            startDate: newWeekData.startDate?.toDate(),
            endDate: newWeekData.endDate?.toDate()
          };
        }
      }

      if (weekToSelect) {
        setSelectedWeek(weekToSelect.id);
        
        // Automatic carryover check
        const isCurrentWeek = currentWeek && weekToSelect.id === currentWeek.id;
        const hasPreviousWeeks = weeksData.length > 1;
        
        if (isCurrentWeek && hasPreviousWeeks) {
          // Find the most recent week that's not the current week
          const previousWeek = weeksData.find(w => w.id !== weekToSelect.id);
          
          if (previousWeek) {
            console.log(`🔍 Checking if carryovers needed from ${previousWeek.id} to ${weekToSelect.id}`);
            
            // Always attempt carryover for current week
            await carryOverFoundVehicles(previousWeek.id, weekToSelect.id);
          }
        }
        
        await initializeWeekWithCarryOvers(db, user.id, weekToSelect.id);
        await loadVehicleDataForWeek(weekToSelect.id);
      }

      // Calculate Month and YTD stats using imported function
      const stats = await calculateMonthAndYTDStats(db, user, vehicles, selectedWeek, vehicleStats);
      setYtdStats(stats);

    } catch (error) {
      console.error("Error loading available weeks:", error);
      setAvailableWeeks([]);
    }
  };

  // Load vehicle data for a specific week with real-time updates
  const loadVehicleDataForWeek = async (weekId) => {
    if (!db || !user || !weekId) return;

    try {
      const weekDoc = await getDoc(doc(db, 'users', user.id, 'vehicleWeeks', weekId));
      if (weekDoc.exists()) {
        const weekData = weekDoc.data();

        let startDateStr = '';
        let endDateStr = '';

        if (weekData.displayRange) {
          const parts = weekData.displayRange.split(' - ');
          startDateStr = parts[0] || '';
          endDateStr = parts[1] || '';
        } else if (weekData.startDate && weekData.endDate) {
          const startDate = weekData.startDate.toDate();
          const endDate = weekData.endDate.toDate();
          const formattedRange = formatDateRange(startDate, endDate);
          const parts = formattedRange.split(' - ');
          startDateStr = parts[0] || '';
          endDateStr = parts[1] || '';

          await updateDoc(doc(db, 'users', user.id, 'vehicleWeeks', weekId), {
            displayRange: formattedRange
          });
        }

        const statsData = {
          totalScans: weekData.totalScans || 0,
          totalFound: weekData.totalFound || 0,
          totalSecured: weekData.totalSecured || 0,
          recoveryRate: weekData.recoveryRate || 0,
          dateRange: {
            start: startDateStr,
            end: endDateStr
          }
        };

        setVehicleStats(statsData);
      }

      const vehiclesRef = collection(db, 'users', user.id, 'vehicleWeeks', weekId, 'vehicles');

      const unsubscribe = onSnapshot(vehiclesRef, (snapshot) => {
        const vehiclesData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        // FILTER OUT VEHICLES THAT SHOULDN'T BE IN THIS USER'S LIST
        const filteredVehicles = vehiclesData.filter(vehicle => {
          // Keep vehicles that:
          // 1. Were added by this user (no teamMemberId or teamMemberId matches user.id)
          // 2. Were secured by this user (securedFromTeammate is true)
          // 3. Don't have copiedFromTeammate flag
          // 4. Don't have fromTeammate flag (unless secured)
          // 5. Don't have bottomStatus (unless secured)
          
          const isOwnVehicle = !vehicle.teamMemberId || vehicle.teamMemberId === user.id;
          const isSecuredFromTeammate = vehicle.securedFromTeammate === true;
          const isNotCopiedFromTeammate = !vehicle.copiedFromTeammate;
          const isNotFromTeammate = !vehicle.fromTeammate || vehicle.status === 'SECURED';
          const hasNoBottomStatus = !vehicle.bottomStatus || vehicle.status === 'SECURED';
          
          // Additional check: if vehicle has addedBy field, check if it matches current user
          const addedByCurrentUser = !vehicle.addedBy || vehicle.addedBy === user.email || vehicle.addedBy === user.displayName || vehicle.addedByUserId === user.id;
          
          return ((isOwnVehicle && addedByCurrentUser) || isSecuredFromTeammate) && 
                 isNotCopiedFromTeammate && 
                 isNotFromTeammate && 
                 hasNoBottomStatus;
        });

        const sortedVehicles = filteredVehicles.sort((a, b) => {
          if (a.status === 'SECURED' && b.status !== 'SECURED') return -1;
          if (b.status === 'SECURED' && a.status !== 'SECURED') return 1;

          if (a.carriedOver && !b.carriedOver) return 1;
          if (!a.carriedOver && b.carriedOver) return -1;

          const dateA = new Date(a.securedDate || a.date || '1900-01-01');
          const dateB = new Date(b.securedDate || b.date || '1900-01-01');
          return dateB - dateA;
        });

        filteredVehicles.forEach(vehicle => {
          if (vehicle.autoSecuredFromTeam && vehicle.securedByTeammate && !vehicle.notificationShown) {
            showTeamSyncNotification(vehicle);

            updateDoc(doc(vehiclesRef, vehicle.id), {
              notificationShown: true
            }).catch(err => console.error("Error updating notification flag:", err));
          }
        });

        setVehicles(sortedVehicles);
        updateVehicleWeekStats(db, user.id, weekId);
      });

      return unsubscribe;

    } catch (error) {
      console.error("Error loading vehicle data for week:", error);
      setVehicles([]);
      setVehicleStats({
        totalScans: 0,
        totalFound: 0,
        totalSecured: 0,
        recoveryRate: 0,
        dateRange: { start: '', end: '' }
      });
    }
  };

  // Show image modal
  const showImageInModal = (imageUrl) => {
    setSelectedImage(imageUrl);
    setShowImageModal(true);
  };

  // Handle file selection
  const handleFileSelect = async (e) => {
    const files = Array.from(e.target.files);
    if (files.length === 0) return;

    setUploadingImages(true);
    setImageUploadProgress(0);

    try {
      const uploadedImages = await handleImageUpload(storage, user, files);

      setNewVehicle(prev => {
        const updated = {
          ...prev,
          images: [...prev.images, ...uploadedImages]
        };
        // Auto-save to localStorage
        localStorage.setItem('vehicleFormDraft', JSON.stringify(updated));
        return updated;
      });

      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      if (cameraInputRef.current) {
        cameraInputRef.current.value = '';
      }
    } catch (error) {
      alert(`Failed to upload images: ${error.message}`);
    } finally {
      setUploadingImages(false);
      setImageUploadProgress(0);
    }
  };

  // Handle file selection for edit form
  const handleEditFileSelect = async (e) => {
    const files = Array.from(e.target.files);
    if (files.length === 0) return;

    setEditUploadingImages(true);
    setEditImageUploadProgress(0);

    try {
      const uploadedImages = await handleImageUpload(storage, user, files);

      setEditVehicleData(prev => ({
        ...prev,
        images: [...(prev.images || []), ...uploadedImages]
      }));

      if (editFileInputRef.current) {
        editFileInputRef.current.value = '';
      }
      if (editCameraInputRef.current) {
        editCameraInputRef.current.value = '';
      }
    } catch (error) {
      alert(`Failed to upload images: ${error.message}`);
    } finally {
      setEditUploadingImages(false);
      setEditImageUploadProgress(0);
    }
  };

  // Remove image from new vehicle
  const removeImageFromNewVehicle = (index) => {
    setNewVehicle(prev => {
      const updated = {
        ...prev,
        images: prev.images.filter((_, i) => i !== index)
      };
      // Auto-save to localStorage
      localStorage.setItem('vehicleFormDraft', JSON.stringify(updated));
      return updated;
    });
  };

  // Remove image from edit vehicle
  const removeImageFromEditVehicle = (index) => {
    setEditVehicleData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }));
  };

  // Get proper vehicle count for display
  const getProperVehicleCount = () => {
    return vehicles ? vehicles.filter(v => v.status === 'SECURED').length : 0;
  };

  // Add new vehicle with enhanced duplicate checking
  const handleAddVehicle = async () => {
    if (!newVehicle.vehicle.trim() || !newVehicle.vin.trim()) {
      alert('Please fill in vehicle name and VIN');
      return;
    }

    if (newVehicle.vin.length > 6) {
      alert('VIN must be 6 digits or less');
      return;
    }

    // Check if it's a DO NOT SECURE status
    if (newVehicle.status === 'DO NOT SECURE') {
      const finalReason = doNotSecureReason === 'OTHER' ? customDoNotSecureReason : doNotSecureReason;
      if (!finalReason) {
        alert('Please select or enter a reason for DO NOT SECURE status');
        return;
      }
      newVehicle.doNotSecureReason = finalReason;
    }

    // FIXED: Case-insensitive VIN duplicate check
    if (newVehicle.vin && newVehicle.vin.trim() !== '') {
      const vinToCheck = newVehicle.vin.trim().toUpperCase();
      const existingVehicle = vehicles.find(v =>
        v.vin && v.vin.trim().toUpperCase() === vinToCheck
      );

      if (existingVehicle && existingVehicle.carriedOver && existingVehicle.status !== 'SECURED') {
        const shouldUpdate = window.confirm(
          `A carryover vehicle with VIN "${newVehicle.vin}" already exists.\n\n` +
          `Would you like to update it to SECURED status instead of adding a new entry?\n\n` +
          `Click OK to update the existing carryover vehicle, or Cancel to add as a new vehicle.`
        );

        if (shouldUpdate) {
          try {
            const updatedVehicleData = {
              ...existingVehicle,
              status: 'SECURED',
              securedDate: newVehicle.securedDate || new Date().toISOString().split('T')[0],
              securedFromCarryover: true,
              securedTimestamp: new Date(),
              notes: newVehicle.notes || existingVehicle.notes || '',
              vinVerified: newVehicle.vinVerified,
              images: [...(existingVehicle.images || []), ...newVehicle.images],
              position: newVehicle.position || existingVehicle.position,
              address: newVehicle.address || existingVehicle.address,
              city: newVehicle.city || existingVehicle.city,
              state: newVehicle.state || existingVehicle.state,
              zipCode: newVehicle.zipCode || existingVehicle.zipCode,
              color: newVehicle.color || existingVehicle.color || '',
              driveType: newVehicle.driveType || existingVehicle.driveType || autoDetectDriveType(existingVehicle.vehicle),
              updatedAt: serverTimestamp()
            };

            await setDoc(doc(db, 'users', user.id, 'vehicleWeeks', selectedWeek, 'vehicles', existingVehicle.id), updatedVehicleData);

            if (team && newVehicle.vin) {
              console.log("🔄 Carryover vehicle secured, syncing across team...");
              const userDisplayName = user.displayName || user.email?.split('@')[0] || 'Team Member';

              await markVINAsSecuredAcrossTeam(
                db,
                team.id,
                user.id,
                newVehicle.vin,
                updatedVehicleData.securedDate,
                userDisplayName
              );
            }

            setNewVehicle({
              vehicle: '',
              vin: '',
              vinVerified: false,
              status: 'PENDING PICKUP',
              date: new Date().toISOString().split('T')[0],
              plateNumber: '',
              accountNumber: '',
              financier: '',
              address: '',
              city: '',
              state: '',
              zipCode: '',
              position: null,
              notes: '',
              images: [],
              color: '',
              driveType: '',
              doNotSecureReason: ''
            });

            setShowAddForm(false);

            playMoneySound(soundEnabled);

            alert(`Successfully updated carryover vehicle "${existingVehicle.vehicle}" to SECURED status!`);
            return;

          } catch (error) {
            console.error("Error updating carryover vehicle:", error);
            alert("Error updating carryover vehicle. Please try again.");
            return;
          }
        }
      }
    }

    try {
      setLoading(true);
      const weekId = selectedWeek || getCurrentWeekId();

      await createWeekIfNeeded(db, user.id, weekId);

      // Auto-detect drive type if not manually set
      const detectedDriveType = autoDetectDriveType(newVehicle.vehicle);
      
      // Build full address from components
      const fullAddress = buildFullAddress(
        newVehicle.address,
        newVehicle.city,
        newVehicle.state,
        newVehicle.zipCode
      );
      
      const vehicleData = {
        ...newVehicle,
        vin: newVehicle.vin.toUpperCase().substring(0, 6),
        color: newVehicle.color || '',
        driveType: detectedDriveType,
        fullAddress: fullAddress,  // Store computed full address
        timestamp: serverTimestamp(),
        addedBy: user.displayName || user.email,
        addedByUserId: user.id,
        weekId: weekId,
        createdAt: serverTimestamp()
      };

      // Include position if available
      if (newVehicle.position) {
        vehicleData.position = newVehicle.position;
      }

      const vehiclesRef = collection(db, 'users', user.id, 'vehicleWeeks', weekId, 'vehicles');
      const docRef = await addDoc(vehiclesRef, vehicleData);

      // Add notification to track new vehicle
      const notification = {
        id: Date.now(),
        type: 'team_sync_success',
        message: `🚗 Added new vehicle: ${vehicleData.vehicle} (VIN: ${vehicleData.vin})`,
        timestamp: new Date()
      };

      setTeamSyncNotifications(prev => [notification, ...prev.slice(0, 4)]);
      setTimeout(() => {
        setTeamSyncNotifications(prev => prev.filter(n => n.id !== notification.id));
      }, 5000);

      // Post to Slack - only post once
      if (team && team.id && !vehicleData.carriedOver) {
        const slackVehicle = formatVehicleForSlack({
          ...vehicleData,
          id: docRef.id,
          teamMemberName: user.displayName || user.email?.split('@')[0] || 'Team Member'
        });
        postVehicleToSlack(team.id, slackVehicle, 'new');
      }

      setNewVehicle({
        vehicle: '',
        vin: '',
        vinVerified: false,
        status: 'PENDING PICKUP',
        date: new Date().toISOString().split('T')[0],
        plateNumber: '',
        accountNumber: '',
        financier: '',
        address: '',
        city: '',
        state: '',
        zipCode: '',
        position: null,
        notes: '',
        images: [],
        color: '',
        driveType: '',
        doNotSecureReason: ''
      });

      // Clear saved draft
      localStorage.removeItem('vehicleFormDraft');

      setShowAddForm(false);
      setShowDoNotSecureReason(false);
      setDoNotSecureReason('');
      setCustomDoNotSecureReason('');
      
      console.log('✅ Vehicle added successfully');

      playNotificationSound(soundEnabled);

    } catch (error) {
      console.error('❌ Error adding vehicle:', error);
      alert('Error adding vehicle. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Update vehicle status
  const handleStatusChange = async (vehicleId, newStatus) => {
    try {
      const weekId = selectedWeek || getCurrentWeekId();
      const vehicleRef = doc(db, 'users', user.id, 'vehicleWeeks', weekId, 'vehicles', vehicleId);

      const originalVehicle = vehicles.find(v => v.id === vehicleId);
      const wasNotSecured = originalVehicle && originalVehicle.status !== 'SECURED';
      const isNowSecured = newStatus === 'SECURED';
      const hasVIN = originalVehicle && originalVehicle.vin && originalVehicle.vin.trim() !== '';

      const updateData = {
        status: newStatus,
        lastUpdated: serverTimestamp()
      };

      if (newStatus === 'SECURED') {
        updateData.securedDate = new Date().toISOString().split('T')[0];
        updateData.securedTimestamp = new Date();
      }

      await updateDoc(vehicleRef, updateData);

      if (wasNotSecured && isNowSecured && hasVIN && team) {
        console.log("🔄 Vehicle just secured with VIN, syncing across team...");

        const userDisplayName = user.displayName || user.email?.split('@')[0] || 'Team Member';

        await markVINAsSecuredAcrossTeam(
          db,
          team.id,
          user.id,
          originalVehicle.vin,
          updateData.securedDate,
          userDisplayName
        );
        
        // Post to Slack when status changes to SECURED
        if (team && team.id) {
          const slackVehicle = formatVehicleForSlack({
            ...originalVehicle,
            status: 'SECURED',
            securedDate: updateData.securedDate,
            securedTimestamp: new Date().toISOString()
          });
          postVehicleToSlack(team.id, slackVehicle, 'secured');
        }
      }

      console.log(`✅ Vehicle status updated to ${newStatus}`);

      if (isNowSecured) {
        playMoneySound(soundEnabled);
      }

    } catch (error) {
      console.error('❌ Error updating vehicle status:', error);
      alert('Error updating status. Please try again.');
    }
  };

  // Delete vehicle
  const handleDeleteVehicle = async (vehicleId) => {
    if (!confirm('Are you sure you want to delete this vehicle? This action cannot be undone.')) return;

    try {
      const weekId = selectedWeek || getCurrentWeekId();
      const vehicleRef = doc(db, 'users', user.id, 'vehicleWeeks', weekId, 'vehicles', vehicleId);
      await deleteDoc(vehicleRef);

      console.log('✅ Vehicle deleted successfully');
    } catch (error) {
      console.error('❌ Error deleting vehicle:', error);
      alert('Error deleting vehicle. Please try again.');
    }
  };

  // Edit handlers
  const handleEditVehicle = (vehicle) => {
    setEditingVehicle(vehicle.id);
    setEditVehicleData({ ...vehicle });
  };

  const handleSaveVehicle = async () => {
    if (!user || !selectedWeek) return;

    try {
      if (editVehicleData.vin && editVehicleData.vin.length > 6) {
        alert('VIN must be 6 digits or less');
        return;
      }

      const originalVehicle = vehicles.find(v => v.id === editingVehicle);
      const wasNotSecured = originalVehicle && originalVehicle.status !== 'SECURED';
      const isNowSecured = editVehicleData.status === 'SECURED';
      const hasVIN = editVehicleData.vin && editVehicleData.vin.trim() !== '';

      // Auto-detect drive type if vehicle name changed
      const detectedDriveType = autoDetectDriveType(editVehicleData.vehicle);
      
      // Build full address from components
      const fullAddress = buildFullAddress(
        editVehicleData.address,
        editVehicleData.city,
        editVehicleData.state,
        editVehicleData.zipCode
      );
      
      const dataToSave = {
        ...editVehicleData,
        vin: editVehicleData.vin ? editVehicleData.vin.toUpperCase().substring(0, 6) : '',
        color: editVehicleData.color || '',
        driveType: detectedDriveType,
        fullAddress: fullAddress,  // Store computed full address
        updatedAt: serverTimestamp()
      };

      await setDoc(doc(db, 'users', user.id, 'vehicleWeeks', selectedWeek, 'vehicles', editingVehicle), dataToSave);

      if (wasNotSecured && isNowSecured && hasVIN && team) {
        console.log("🔄 Vehicle just secured with VIN, syncing across team...");

        const userDisplayName = user.displayName || user.email?.split('@')[0] || 'Team Member';

        await markVINAsSecuredAcrossTeam(
          db,
          team.id,
          user.id,
          dataToSave.vin,
          dataToSave.securedDate,
          userDisplayName
        );
        
        // Post to Slack when edited vehicle is marked as secured
        if (team && team.id) {
          const slackVehicle = formatVehicleForSlack({
            ...dataToSave,
            id: editingVehicle
          });
          postVehicleToSlack(team.id, slackVehicle, 'secured');
        }

        playMoneySound(soundEnabled);
      }

      setEditingVehicle(null);
      setEditVehicleData({});

    } catch (error) {
      console.error("Error saving vehicle data:", error);
    }
  };

  const handleCancelEdit = () => {
    setEditingVehicle(null);
    setEditVehicleData({});
  };

  const handleEditInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    let processedValue = value;

    if (name === 'vin') {
      processedValue = value.substring(0, 6).toUpperCase();
    }

    if (type === 'checkbox') {
      processedValue = checked;
    }

    setEditVehicleData(prev => ({
      ...prev,
      [name]: processedValue
    }));
  };

  const handleNewVehicleChange = (e) => {
    const { name, value, type, checked } = e.target;
    let processedValue = value;

    if (name === 'vin') {
      processedValue = value.substring(0, 6).toUpperCase();
    }

    if (type === 'checkbox') {
      processedValue = checked;
    }

    if (name === 'status') {
      processedValue = value;
      // Show DO NOT SECURE reason selector if that status is selected
      setShowDoNotSecureReason(value === 'DO NOT SECURE');
      if (value !== 'DO NOT SECURE') {
        setDoNotSecureReason('');
        setCustomDoNotSecureReason('');
      }
    }

    setNewVehicle(prev => {
      const updated = {
        ...prev,
        [name]: processedValue
      };
      // Auto-save to localStorage
      localStorage.setItem('vehicleFormDraft', JSON.stringify(updated));
      return updated;
    });
  };

  const handleWeekChange = (e) => {
    const newWeekId = e.target.value;
    setSelectedWeek(newWeekId);
    loadVehicleDataForWeek(newWeekId);
  };

  // Scan editing handlers
  const handleEditScans = () => {
    setEditingScans(true);
    setEditScanAmount(vehicleStats.totalScans);
  };

  const handleSaveScans = async () => {
    if (!user || !selectedWeek) return;

    try {
      const scanAmount = parseInt(editScanAmount);

      await updateDoc(doc(db, 'users', user.id, 'vehicleWeeks', selectedWeek), {
        totalScans: scanAmount,
        updatedAt: serverTimestamp()
      });

      const updatedStats = {
        ...vehicleStats,
        totalScans: scanAmount
      };

      setVehicleStats(updatedStats);

      // Recalculate Month and YTD stats
      const stats = await calculateMonthAndYTDStats(db, user, vehicles, selectedWeek, updatedStats);
      setYtdStats(stats);

      setEditingScans(false);
    } catch (error) {
      console.error("Error saving scan amount:", error);
    }
  };

  const handleCancelEditScans = () => {
    setEditingScans(false);
  };

  const handleScanInputChange = (e) => {
    setEditScanAmount(e.target.value);
  };

  // Notes handlers
  const handleEditNotes = (vehicleId, currentNotes) => {
    setEditingNotes(vehicleId);
    setTempNotes(currentNotes || '');
  };

  const handleSaveNotes = async () => {
    if (!user || !selectedWeek || !editingNotes) return;

    try {
      await updateDoc(doc(db, 'users', user.id, 'vehicleWeeks', selectedWeek, 'vehicles', editingNotes), {
        notes: tempNotes,
        updatedAt: serverTimestamp()
      });

      setEditingNotes(null);
      setTempNotes('');
    } catch (error) {
      console.error("Error saving notes:", error);
    }
  };

  const handleCancelNotes = () => {
    setEditingNotes(null);
    setTempNotes('');
  };

  // Toggle actions menu for mobile view
  const toggleActionsMenu = (vehicleId) => {
    setShowActions(prev => ({
      ...prev,
      [vehicleId]: !prev[vehicleId]
    }));
  };

  // SIMPLE & DIRECT PDF Export function - tries autoTable directly
  const exportToPDF = () => {
    console.log('📊 Starting PDF export...');
    
    if (!vehicles || vehicles.length === 0) {
      alert("No vehicle data to export. Please add some vehicles first.");
      return;
    }
    
    console.log('📊 Exporting vehicles to PDF:', vehicles.length, 'vehicles');
    
    // Create loading indicator
    const loadingEl = document.createElement('div');
    loadingEl.style.position = 'fixed';
    loadingEl.style.top = '50%';
    loadingEl.style.left = '50%';
    loadingEl.style.transform = 'translate(-50%, -50%)';
    loadingEl.style.padding = '20px';
    loadingEl.style.background = 'rgba(0, 0, 0, 0.9)';
    loadingEl.style.color = 'white';
    loadingEl.style.borderRadius = '8px';
    loadingEl.style.zIndex = '9999';
    loadingEl.style.textAlign = 'center';
    loadingEl.style.fontFamily = 'Arial, sans-serif';
    loadingEl.innerHTML = `
      <div style="margin-bottom: 15px; font-size: 16px; font-weight: bold;">Generating PDF Report...</div>
      <div style="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db ; border-radius: 50%; margin: 0 auto; animation: spin 1s linear infinite;"></div>
      <div style="margin-top: 10px; font-size: 12px; color: #ccc;">Please wait...</div>
      <style>@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }</style>
    `;
    document.body.appendChild(loadingEl);

    // Simple library loading function
    const loadLibraries = () => {
      return new Promise((resolve, reject) => {
        // Check if jsPDF already exists
        if (window.jspdf && window.jspdf.jsPDF) {
          console.log('✅ jsPDF already available');
          // Try to load autoTable if not present
          if (!document.querySelector('script[src*="autotable"]')) {
            const autoTableScript = document.createElement('script');
            autoTableScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js';
            autoTableScript.onload = () => {
              console.log('✅ autoTable loaded');
              setTimeout(resolve, 1000); // Give time to attach
            };
            autoTableScript.onerror = () => resolve(); // Continue even if autoTable fails
            document.head.appendChild(autoTableScript);
          } else {
            resolve();
          }
          return;
        }

        // Load jsPDF first
        const jsPdfScript = document.createElement('script');
        jsPdfScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
        
        jsPdfScript.onload = () => {
          console.log('✅ jsPDF loaded');
          // Load autoTable
          const autoTableScript = document.createElement('script');
          autoTableScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js';
          
          autoTableScript.onload = () => {
            console.log('✅ autoTable loaded');
            setTimeout(resolve, 1000); // Give time for plugin to attach
          };
          autoTableScript.onerror = () => resolve(); // Continue even if autoTable fails
          document.head.appendChild(autoTableScript);
        };
        
        jsPdfScript.onerror = () => reject(new Error('Failed to load jsPDF'));
        document.head.appendChild(jsPdfScript);
      });
    };

    // Generate PDF with fallback strategy
    const generatePDF = () => {
      try {
        // Get jsPDF
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF({
          orientation: 'landscape',
          unit: 'mm',
          format: 'a4'
        });
        
        const pageWidth = doc.internal.pageSize.width;
        const pageHeight = doc.internal.pageSize.height;
        
        // Header Section
        doc.setFillColor(52, 152, 219);
        doc.rect(0, 0, pageWidth, 35, 'F');
        
        doc.setTextColor(255, 255, 255);
        doc.setFontSize(22);
        doc.setFont('helvetica', 'bold');
        doc.text('VEHICLE TRACKING REPORT', pageWidth / 2, 22, { align: 'center' });
        
        // Agent information
        doc.setTextColor(0, 0, 0);
        doc.setFontSize(12);
        doc.setFont('helvetica', 'bold');
        doc.text(`Agent: ${getUserDisplayName()}`, 20, 50);
        doc.text(`Report Date: ${new Date().toLocaleDateString()}`, 20, 58);
        doc.text(`Week: ${getWeekDisplayName()}`, 20, 66);
        
        // Performance Summary
        const weeklyStats = calculateWeeklyStats(vehicles);
        
        doc.setFillColor(240, 248, 255);
        doc.setDrawColor(52, 152, 219);
        doc.setLineWidth(1);
        doc.rect(10, 75, pageWidth - 20, 25, 'FD');
        
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(14);
        doc.setTextColor(52, 152, 219);
        doc.text('PERFORMANCE SUMMARY', 15, 88);
        
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(10);
        doc.setTextColor(0, 0, 0);
        doc.text('WEEK:', 15, 95);
        
        doc.setFont('helvetica', 'normal');
        doc.setFontSize(9);
        doc.text(`Scans: ${vehicleStats.totalScans || 0}`, 55, 95);
        doc.text(`Found: ${weeklyStats.weeklyFound}`, 110, 95);
        doc.text(`Secured: ${weeklyStats.weeklySecured}`, 160, 95);
        doc.text(`Carryover: ${weeklyStats.totalCarryover}`, 210, 95);

        let currentY = 110;

        // Try to use autoTable - with multiple approaches
        const tableHeaders = [
          'Date', 'Vehicle', 'VIN', 'Verified', 'Color', 'Drive', 'Plate #', 'Account #', 
          'Financier', 'Address', 'Status', 'C/O', 'Secured Date', 'Team', 'Bottom Status', 'Notes'
        ];

        const tableData = vehicles.map(vehicle => [
          vehicle.date || '',
          vehicle.vehicle || '',
          vehicle.vin || '',
          vehicle.vinVerified ? 'YES' : 'NO',
          vehicle.color || '',
          vehicle.driveType || '',
          vehicle.plateNumber || '',
          vehicle.accountNumber || '',
          vehicle.financier || '',
          vehicle.fullAddress || buildFullAddress(vehicle.address, vehicle.city, vehicle.state, vehicle.zipCode) || '',
          vehicle.status || '',
          vehicle.carriedOver ? 'YES' : 'NO',
          vehicle.securedDate || '',
          vehicle.autoSecuredFromTeam ? 'TEAM' : 
          (vehicle.fromTeammate ? 'TEAM' : 'SELF'),
          vehicle.bottomStatus || vehicle.doNotSecureReason || '',
          vehicle.notes && vehicle.notes.length > 20 ? 
            vehicle.notes.substring(0, 20) + '...' : vehicle.notes || ''
        ]);

        // Try multiple ways to access autoTable
        let autoTableWorked = false;

        try {
          // Method 1: doc.autoTable (most common)
          if (typeof doc.autoTable === 'function') {
            console.log('📊 Using doc.autoTable method');
            doc.autoTable({
              head: [tableHeaders],
              body: tableData,
              startY: currentY,
              styles: {
                fontSize: 7,
                cellPadding: 2,
                overflow: 'linebreak',
                halign: 'left'
              },
              headStyles: {
                fillColor: [41, 128, 185],
                textColor: [255, 255, 255],
                fontStyle: 'bold',
                fontSize: 8,
                halign: 'center'
              },
              alternateRowStyles: {
                fillColor: [248, 249, 250]
              },
              didParseCell: function(data) {
                // Status column
                if (data.column.index === 10) {
                  const status = data.cell.raw;
                  if (status === 'SECURED') {
                    data.cell.styles.fillColor = [40, 167, 69];
                    data.cell.styles.textColor = [255, 255, 255];
                    data.cell.styles.fontStyle = 'bold';
                  } else if (status === 'FOUND') {
                    data.cell.styles.fillColor = [255, 193, 7];
                    data.cell.styles.textColor = [0, 0, 0];
                    data.cell.styles.fontStyle = 'bold';
                  } else if (status === 'PENDING PICKUP') {
                    data.cell.styles.fillColor = [33, 150, 243];
                    data.cell.styles.textColor = [255, 255, 255];
                    data.cell.styles.fontStyle = 'bold';
                  } else if (status === 'DO NOT SECURE') {
                    data.cell.styles.fillColor = [156, 39, 176];
                    data.cell.styles.textColor = [255, 255, 255];
                    data.cell.styles.fontStyle = 'bold';
                  }
                }
                // VIN Verified column
                if (data.column.index === 3 && data.cell.raw === 'YES') {
                  data.cell.styles.fillColor = [40, 167, 69];
                  data.cell.styles.textColor = [255, 255, 255];
                  data.cell.styles.fontStyle = 'bold';
                }
                // Carryover column
                if (data.column.index === 11 && data.cell.raw === 'YES') {
                  data.cell.styles.fillColor = [255, 152, 0];
                  data.cell.styles.textColor = [255, 255, 255];
                  data.cell.styles.fontStyle = 'bold';
                }
                // Team column
                if (data.column.index === 13 && data.cell.raw === 'TEAM') {
                  data.cell.styles.fillColor = [138, 43, 226];
                  data.cell.styles.textColor = [255, 255, 255];
                  data.cell.styles.fontStyle = 'bold';
                }
                // Bottom Status column
                if (data.column.index === 14 && data.cell.raw && data.cell.raw.length > 0) {
                  data.cell.styles.fillColor = [220, 53, 69];
                  data.cell.styles.textColor = [255, 255, 255];
                  data.cell.styles.fontStyle = 'bold';
                }
              },
              margin: { left: 10, right: 10 }
            });
            autoTableWorked = true;
          }
        } catch (e) {
          console.log('❌ doc.autoTable failed:', e.message);
        }

        // Method 2: window.jspdf.autoTable
        if (!autoTableWorked) {
          try {
            if (window.jspdf && typeof window.jspdf.autoTable === 'function') {
              console.log('📊 Using window.jspdf.autoTable method');
              window.jspdf.autoTable(doc, {
                head: [tableHeaders],
                body: tableData,
                startY: currentY,
                styles: { fontSize: 7, cellPadding: 2 },
                headStyles: { fillColor: [41, 128, 185], textColor: [255, 255, 255], fontStyle: 'bold', fontSize: 8 },
                alternateRowStyles: { fillColor: [248, 249, 250] },
                margin: { left: 10, right: 10 }
              });
              autoTableWorked = true;
            }
          } catch (e) {
            console.log('❌ window.jspdf.autoTable failed:', e.message);
          }
        }

        // Fallback: Create a simple but clean table manually
        if (!autoTableWorked) {
          console.log('📊 Creating manual table as fallback');
          
          doc.setFontSize(12);
          doc.setFont('helvetica', 'bold');
          doc.setTextColor(0, 0, 0);
          doc.text('VEHICLE DATA', 15, currentY);
          currentY += 10;

          // Table header
          doc.setFillColor(41, 128, 185);
          doc.rect(10, currentY, pageWidth - 20, 8, 'F');
          doc.setTextColor(255, 255, 255);
          doc.setFontSize(8);
          doc.setFont('helvetica', 'bold');

          let xPos = 15;
          const colWidths = [20, 30, 15, 15, 15, 10, 20, 20, 25, 40, 20, 12, 20, 15, 25, 30];
          tableHeaders.forEach((header, i) => {
            doc.text(header, xPos, currentY + 5);
            xPos += colWidths[i];
          });

          currentY += 10;
          doc.setTextColor(0, 0, 0);
          doc.setFont('helvetica', 'normal');

          // Table rows
          vehicles.forEach((vehicle, index) => {
            if (currentY > pageHeight - 30) return; // Avoid page overflow

            // Alternate row colors
            if (index % 2 === 0) {
              doc.setFillColor(248, 249, 250);
              doc.rect(10, currentY, pageWidth - 20, 6, 'F');
            }

            xPos = 15;
            const rowData = [
              vehicle.date || '',
              vehicle.vehicle || '',
              vehicle.vin || '',
              vehicle.vinVerified ? 'YES' : 'NO',
              vehicle.color || '',
              vehicle.driveType || '',
              vehicle.plateNumber || '',
              vehicle.accountNumber || '',
              vehicle.financier || '',
              vehicle.fullAddress || buildFullAddress(vehicle.address, vehicle.city, vehicle.state, vehicle.zipCode) || '',
              vehicle.status || '',
              vehicle.carriedOver ? 'YES' : 'NO',
              vehicle.securedDate || '',
              vehicle.autoSecuredFromTeam ? 'TEAM' : (vehicle.fromTeammate ? 'TEAM' : 'SELF'),
              vehicle.bottomStatus || vehicle.doNotSecureReason || '',
              vehicle.notes ? (vehicle.notes.length > 20 ? vehicle.notes.substring(0, 20) + '...' : vehicle.notes) : ''
            ];

            rowData.forEach((data, i) => {
              // Color coding for status
              if (i === 10 && data === 'SECURED') {
                doc.setTextColor(40, 167, 69);
                doc.setFont('helvetica', 'bold');
              } else if (i === 10 && data === 'FOUND') {
                doc.setTextColor(255, 140, 0);
                doc.setFont('helvetica', 'bold');
              } else if (i === 10 && data === 'DO NOT SECURE') {
                doc.setTextColor(156, 39, 176);
                doc.setFont('helvetica', 'bold');
              } else {
                doc.setTextColor(0, 0, 0);
                doc.setFont('helvetica', 'normal');
              }

              doc.text(data.toString(), xPos, currentY + 4);
              xPos += colWidths[i];
            });

            currentY += 6;
          });
        }

        // Footer
        const footerY = pageHeight - 15;
        doc.setDrawColor(200, 200, 200);
        doc.line(10, footerY - 5, pageWidth - 10, footerY - 5);

        doc.setFontSize(8);
        doc.setFont('helvetica', 'normal');
        doc.setTextColor(100, 100, 100);
        doc.text('Generated by NWRepo Analytics System', 15, footerY);
        doc.text('Page 1 of 1 • ' + new Date().toLocaleString(), pageWidth - 15, footerY, { align: 'right' });

        // Save the PDF
        const fileName = `Vehicle_Report_${getUserDisplayName()}_${getWeekDisplayName().replace(/[^a-zA-Z0-9]/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;
        doc.save(fileName);

        console.log('✅ PDF generated successfully');
        playNotificationSound(soundEnabled);

      } catch (error) {
        console.error("❌ Error generating PDF:", error);
        throw error;
      }
    };

    // Start the process
    loadLibraries()
      .then(() => {
        console.log('📚 Libraries ready, generating PDF...');
        generatePDF();
      })
      .catch((error) => {
        console.error("❌ Error:", error);
        alert(`Failed to generate PDF: ${error.message}\n\nPlease try refreshing the page and attempting again.`);
      })
      .finally(() => {
        if (loadingEl && loadingEl.parentNode) {
          loadingEl.parentNode.removeChild(loadingEl);
        }
      });
  };

  // Print functionality
  const handlePrint = () => {
    if (vehicles.length === 0) {
      alert("No vehicle data to export.");
      return;
    }

    setShowPrintView(true);

    setTimeout(() => {
      window.print();
      setTimeout(() => {
        setShowPrintView(false);
      }, 500);
    }, 300);
  };

  // Print view component
  const PrintView = () => {
    const currentDate = new Date().toLocaleDateString();
    const weeklyStats = calculateWeeklyStats(vehicles);

    return (
      <div ref={printRef} className="print-container p-8 bg-white text-black" style={{ display: 'none' }}>
        <style type="text/css" media="print">
          {`
          @page { 
            size: landscape;
            margin: 0.5in;
          }

          body { 
            font-family: Arial, sans-serif;
            color: black;
            background-color: white;
          }

          .print-container {
            display: block !important;
            background-color: white;
            color: black;
          }

          table {
            width: 100%;
            border-collapse: collapse;
          }

          th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
          }

          th {
            background-color: #f2f2f2;
            font-weight: bold;
          }

          tr:nth-child(even) {
            background-color: #f9f9f9;
          }
          `}
        </style>

        <div className="print-header">
          <div className="text-2xl font-bold mb-4 text-center">Vehicle Tracking Report</div>
          <div className="mb-2">User: {getUserDisplayName()}</div>
          <div className="mb-2">Week: {vehicleStats.dateRange.start} - {vehicleStats.dateRange.end}</div>
          <div className="mb-2">Total Vehicles: {getProperVehicleCount()}</div>
          <div className="mb-4">Report Generated: {currentDate}</div>
        </div>

        <table>
          <thead>
            <tr>
              <th>Date</th>
              <th>Vehicle</th>
              <th>VIN</th>
              <th>Verified</th>
              <th>Color</th>
              <th>Drive</th>
              <th>Plate #</th>
              <th>Account #</th>
              <th>Financier</th>
              <th>Address</th>
              <th>Status</th>
              <th>C/O</th>
              <th>Secured Date</th>
              <th>Team</th>
              <th>Bottom Status</th>
              <th>Notes</th>
            </tr>
          </thead>
          <tbody>
            {vehicles.length === 0 ? (
              <tr>
                <td colSpan="16" style={{ textAlign: 'center' }}>No vehicles found for this week.</td>
              </tr>
            ) : (
              vehicles.map((vehicle, index) => (
                <tr key={index}>
                  <td>{vehicle.date || ''}</td>
                  <td>{vehicle.vehicle || ''}</td>
                  <td>{vehicle.vin || ''}</td>
                  <td>{vehicle.vinVerified ? 'YES' : 'NO'}</td>
                  <td>{vehicle.color || ''}</td>
                  <td>{vehicle.driveType || ''}</td>
                  <td>{vehicle.plateNumber || ''}</td>
                  <td>{vehicle.accountNumber || ''}</td>
                  <td>{vehicle.financier || ''}</td>
                  <td>{vehicle.fullAddress || buildFullAddress(vehicle.address, vehicle.city, vehicle.state, vehicle.zipCode) || ''}</td>
                  <td>{vehicle.status || ''}</td>
                  <td>{vehicle.carriedOver ? 'YES' : 'NO'}</td>
                  <td>{vehicle.securedDate || ''}</td>
                  <td>{vehicle.autoSecuredFromTeam ? 'TEAM' : (vehicle.fromTeammate ? 'TEAM' : 'SELF')}</td>
                  <td>{vehicle.bottomStatus || vehicle.doNotSecureReason || ''}</td>
                  <td>{vehicle.notes || ''}</td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    );
  };

  // Sound toggle component
  const SoundToggle = () => (
    <div className="flex items-center">
      <button
        onClick={() => setSoundEnabled(!soundEnabled)}
        className={`flex items-center ${soundEnabled ? 'text-blue-400' : 'text-gray-500'}`}
        title={soundEnabled ? "Disable notification sounds" : "Enable notification sounds"}
      >
        <span className="mr-1 text-lg">
          {soundEnabled ? '🔊' : '🔇'}
        </span>
        <span className="text-xs hidden sm:inline">
          {soundEnabled ? 'Sound On' : 'Sound Off'}
        </span>
      </button>
    </div>
  );

  // Mobile-friendly input styles
  const mobileInputStyles = isMobile 
    ? "w-full bg-gray-900 border-2 border-gray-600 rounded-lg px-4 py-3 text-white text-base focus:border-blue-500 focus:outline-none"
    : "w-full bg-gray-900 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm focus:border-blue-500 focus:outline-none";

  const mobileLabelStyles = isMobile
    ? "block text-gray-300 text-sm mb-2 font-semibold"
    : "block text-gray-300 text-xs mb-1 font-semibold";

  // Initialize Firebase and load data
  useEffect(() => {
    let isMounted = true;

    const initializeApp = async () => {
      try {
        console.log('🚀 Starting standalone vehicle tracker initialization...');

        const targetUserId = getUserIdFromUrl();

        if (!targetUserId) {
          const currentUrl = window.location.href;
          const errorMessage = 
            `No user ID provided in URL.\n\n` +
            `Current URL: ${currentUrl}\n\n` +
            `Expected formats:\n` +
            `• ${window.location.origin}/vehicles?user=YOUR_USER_ID\n` +
            `• ${window.location.origin}/vehicles/YOUR_USER_ID\n\n` +
            `Contact your administrator for your USER_ID.`;

          console.error('❌ No user ID found:', errorMessage);

          if (isMounted) {
            setInitError(errorMessage);
            setLoading(false);
          }
          return;
        }

        const { firestore, storageInstance } = await initializeFirebase(targetUserId);

        if (isMounted) {
          setDb(firestore);
          setStorage(storageInstance);
        }

        const { userInfo, profileData } = await loadUserData(firestore, targetUserId);

        if (isMounted) {
          setUser(userInfo);
          setUserProfile(profileData);
          
          const canSecure = checkUserPermissions(userInfo, profileData);
          setCanSecureVehicles(canSecure);
          console.log('🔐 User permissions checked - Can secure vehicles:', canSecure);
        }

        const userTeam = await findUserTeam(firestore, targetUserId);

        if (isMounted) {
          setTeam(userTeam);
          setLoading(false);
        }

      } catch (err) {
        console.error("❌ Error initializing:", err);
        if (isMounted) {
          setInitError(err.message || "Failed to load user data");
          setLoading(false);
        }
      }
    };

    initializeApp();

    return () => {
      isMounted = false;
    };
  }, []);

  // Load data when user is ready
  useEffect(() => {
    if (user && db) {
      console.log('🚗 Loading vehicle data...');
      loadAvailableWeeks();
      createWeekIfNeeded(db, user.id, getCurrentWeekId());
    }
  }, [user, db]);

  // NEW: Load orders when team is available
  useEffect(() => {
    if (db && team) {
      console.log('📋 Loading orders for team...');
      const unsubscribe = loadOrders();
      return () => {
        if (typeof unsubscribe === 'function') {
          unsubscribe();
        }
      };
    }
  }, [db, team, loadOrders]);

  // FIXED: Load team vehicles when team is available OR when selectedWeek changes
  useEffect(() => {
    if (db && team && user) {
      console.log('👥 Loading team vehicles for map...', selectedWeek ? `(week: ${selectedWeek})` : '(current week)');
      loadTeamVehicles();
    }
  }, [db, team, user, selectedWeek, loadTeamVehicles]);

  // NEW: Load chat messages when team is available
  useEffect(() => {
    if (db && team && user && isFeatureOpen('chat')) {
      console.log('💬 Loading chat messages...');
      const unsubscribe = loadChatMessages();
      return () => {
        if (typeof unsubscribe === 'function') {
          unsubscribe();
        }
      };
    }
  }, [db, team, user, isFeatureOpen('chat'), loadChatMessages]);

  // NEW: Load team locations when trails feature is open
  useEffect(() => {
    if (db && team && user && isFeatureOpen('trails')) {
      console.log('🛤️ Loading team locations...');
      const unsubscribe = loadTeamLocations();
      return () => {
        if (typeof unsubscribe === 'function') {
          unsubscribe();
        }
      };
    }
  }, [db, team, user, isFeatureOpen('trails'), loadTeamLocations]);

  // Check for team-secured vehicles
  useEffect(() => {
    if (vehicles && vehicles.length > 0) {
      vehicles.forEach(vehicle => {
        if (vehicle.autoSecuredFromTeam && vehicle.securedByTeammate && !vehicle.notificationShown) {
          showTeamSyncNotification(vehicle);
          vehicle.notificationShown = true;
        }
      });
    }
  }, [vehicles]);

  // Recalculate monthly and YTD stats when vehicles change
  useEffect(() => {
    if (user && db && vehicles.length > 0) {
      calculateMonthAndYTDStats(db, user, vehicles, selectedWeek, vehicleStats).then(stats => {
        setYtdStats(stats);
      });
    }
  }, [vehicles]);

  // Detect viewport size for responsive layout
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      setIsMobile(width < 768);
      setIsTablet(width >= 768 && width < 1024);
    };

    handleResize(); // Call immediately to set initial state

    window.addEventListener('resize', handleResize);

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Update permissions when user profile changes
  useEffect(() => {
    if (user && userProfile) {
      const canSecure = checkUserPermissions(user, userProfile);
      setCanSecureVehicles(canSecure);
      console.log('🔐 Permissions updated - Can secure vehicles:', canSecure);
    }
  }, [user, userProfile]);

  // Error boundary for Firebase/App errors
  if (initError) {
    return (
      <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center p-4">
        <div className="text-center max-w-lg mx-auto">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold mb-4">Error Loading Vehicle Tracker</h1>
          <div className="text-gray-300 mb-6 bg-gray-800 p-4 rounded-lg text-left text-sm max-h-64 overflow-y-auto">
            <pre className="whitespace-pre-wrap font-mono text-xs">{initError}</pre>
          </div>
          <div className="space-y-2">
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded font-medium mr-3"
            >
              Retry
            </button>
            <button
              onClick={() => window.history.back()}
              className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded font-medium"
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-300 text-lg">Loading vehicle tracker...</p>
          <p className="text-gray-500 text-sm mt-2">Setting up Firebase connection...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
        <div className="text-center">
          <div className="text-yellow-500 text-6xl mb-4">🔍</div>
          <h1 className="text-2xl font-bold mb-2">User Not Found</h1>
          <p className="text-gray-300">No user found with the provided ID</p>
        </div>
      </div>
    );
  }

  // Get weekly stats for display
  const weeklyStats = calculateWeeklyStats(vehicles);

  // Main render
  return (
    <>
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 text-white">
        {/* Header */}
        <div className="bg-black bg-opacity-50 backdrop-blur-lg shadow-xl border-b border-gray-700">
          <div className="px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="text-3xl mr-3 animate-pulse">{user.avatar}</div>
                <div>
                  <h1 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
                    Vehicle Tracker
                  </h1>
                  <p className="text-xs sm:text-sm text-gray-300">
                    {user.displayName} • {team ? team.name : 'No Team'} • {user.jobTitle}
                    {canSecureVehicles && (
                      <span className="ml-2 bg-green-600 text-white text-xs px-2 py-1 rounded-full">
                        🚚 Can Secure
                      </span>
                    )}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                {/* NEW: Features Button */}
                <button
                  onClick={() => setShowFeaturesSelector(true)}
                  className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white px-4 py-2 rounded-lg shadow-lg flex items-center gap-2 font-semibold transition-all transform hover:scale-105"
                >
                  <span className="text-lg">⚙️</span>
                  <span className="hidden sm:inline">Features</span>
                  {activeFeature && (
                    <span className="bg-white text-purple-600 text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold">
                      1
                    </span>
                  )}
                </button>

                <div className="text-right hidden lg:block">
                  <div className="text-xs text-gray-400">Full-Screen Map Mode</div>
                  <div className="text-sm text-blue-300 font-semibold">recoveriqs.net</div>
                  <div className="text-xs text-gray-500">
                    {vehicles.length > 0 && `Last sync: ${new Date().toLocaleTimeString()}`}
                  </div>
                </div>
                
                <SoundToggle />
              </div>
            </div>
          </div>
        </div>

        {/* Team Sync Notifications */}
        {teamSyncNotifications.length > 0 && (
          <div className="fixed top-20 left-1/2 transform -translate-x-1/2 z-50 space-y-2">
            {teamSyncNotifications.map(notification => (
              <div 
                key={notification.id} 
                className={`bg-opacity-90 backdrop-blur-lg border rounded-xl p-3 text-sm shadow-lg ${getNotificationColor(notification.type)}`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <span className="font-semibold">{notification.message}</span>
                  </div>
                  <button 
                    onClick={() => setTeamSyncNotifications(prev => prev.filter(n => n.id !== notification.id))}
                    className="text-current hover:opacity-75 ml-2 text-lg"
                    title="Dismiss notification"
                  >
                    ✕
                  </button>
                </div>
                <div className="text-xs opacity-75 mt-1">
                  {notification.timestamp.toLocaleTimeString()}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Full-Screen Map Container */}
        <div className="relative w-full h-screen">
          {/* Vehicle Map Display - FULL SCREEN */}
          <VehicleMapDisplay 
            vehicles={showPersonalVehiclesOnMap ? vehicles : []} // Only show personal vehicles if toggled on
            teamVehicles={teamVehicles} // Team vehicles always show
            orders={orders}
            db={db}
            user={user}
            userProfile={userProfile}
            team={team}
            selectedWeek={selectedWeek}
            onVehicleDeleted={handleVehicleDeletedFromMap}
            selectedVehicleId={selectedVehicleId}
            onVehicleSelect={handleVehicleSelect}
            selectedOrderId={selectedOrderId}
            onOrderSelect={handleOrderSelect}
            onOrderNavigate={handleOrderNavigate}
            onOrderSecure={handleOrderSecure}
            onTeamVehiclesUpdate={loadTeamVehicles}
            // NEW: Trail management props
            onTrailTimeRangeChange={handleTrailTimeRangeChange}
            onTrailToggle={toggleTrails}
            onDeleteAllTrails={deleteAllTrails}
            onDeleteUserTrails={deleteUserTrails}
            onDeleteOwnTrail={() => deleteUserTrails(user.id)}
            // NEW: Trail state props
            trailTimeRange={trailTimeRange}
            showTrails={showTrails}
            teammateLocations={teammateLocations}
            locationHistory={locationHistory}
            teammateColors={teammateColors}
            allTeamMembers={allTeamMembers}
            isAdmin={isAdmin}
            userHasTrail={locationHistory.has(user.id)}
            // NEW: Chat props
            chatMessages={chatMessages}
            newChatMessage={newChatMessage}
            setNewChatMessage={setNewChatMessage}
            onSendChatMessage={sendChatMessage}
            userProfilePictures={userProfilePictures}
          />

          {/* Team Vehicles Component - Now as overlay */}
          {isFeatureOpen('team-vehicles') && (
            <div className="overlay-panel visible">
              <div className="overlay-panel-header">
                <div className="overlay-panel-title">
                  👥 Team Vehicles
                </div>
                <button 
                  className="overlay-panel-close" 
                  onClick={() => toggleFeature('team-vehicles')}
                >
                  ×
                </button>
              </div>
              <div className="overlay-panel-content">
                <TeamVehicleTracker 
                  db={db}
                  user={user}
                  userProfile={userProfile}
                  team={team}
                  selectedWeek={selectedWeek}
                  vehicles={vehicles}
                  setVehicles={setVehicles}
                  canSecureVehicles={canSecureVehicles}
                  soundEnabled={soundEnabled}
                  updateVehicleWeekStats={updateVehicleWeekStats}
                  showImageInModal={showImageInModal}
                  teamSyncNotifications={teamSyncNotifications}
                  setTeamSyncNotifications={setTeamSyncNotifications}
                  setTeamVehicles={setTeamVehicles}
                  selectedVehicleId={selectedVehicleId}
                  onVehicleSelect={handleVehicleSelect}
                  orders={orders}
                  selectedOrderId={selectedOrderId}
                  onOrderSelect={handleOrderSelect}
                />
              </div>
            </div>
          )}

          {/* Statistics Panel */}
          {isFeatureOpen('stats') && (
            <div className="overlay-panel visible">
              <div className="overlay-panel-header">
                <div className="overlay-panel-title">
                  📊 Statistics & Performance
                </div>
                <button 
                  className="overlay-panel-close" 
                  onClick={() => toggleFeature('stats')}
                >
                  ×
                </button>
              </div>
              <div className="overlay-panel-content">
                <StatsCards 
                  vehicleStats={vehicleStats}
                  weeklyStats={weeklyStats}
                  editingScans={editingScans}
                  editScanAmount={editScanAmount}
                  handleEditScans={handleEditScans}
                  handleSaveScans={handleSaveScans}
                  handleCancelEditScans={handleCancelEditScans}
                  handleScanInputChange={handleScanInputChange}
                />
                <div className="mt-4">
                  <MonthlyAndYTDStats ytdStats={ytdStats} />
                </div>
              </div>
            </div>
          )}

          {/* My Vehicles Panel */}
          {isFeatureOpen('vehicles') && (
            <div className="overlay-panel visible">
              <div className="overlay-panel-header">
                <div className="overlay-panel-title">
                  🚗 My Vehicles ({vehicles.length})
                </div>
                <button 
                  className="overlay-panel-close" 
                  onClick={() => toggleFeature('vehicles')}
                >
                  ×
                </button>
              </div>
              <div className="overlay-panel-content">
                {/* Show Personal Vehicles on Map Toggle */}
                <div className="mb-4 bg-blue-900 bg-opacity-30 rounded-lg p-3 border border-blue-600">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-blue-300 font-semibold">🗺️ Map Display</h4>
                      <p className="text-blue-200 text-sm">Toggle visibility of your personal vehicles on the map</p>
                    </div>
                    <button
                      onClick={() => setShowPersonalVehiclesOnMap(!showPersonalVehiclesOnMap)}
                      className={`px-4 py-2 rounded-lg font-semibold transition-all ${
                        showPersonalVehiclesOnMap 
                          ? 'bg-green-600 hover:bg-green-700 text-white' 
                          : 'bg-gray-600 hover:bg-gray-700 text-white'
                      }`}
                    >
                      {showPersonalVehiclesOnMap ? '👁️ Showing on Map' : '👁️‍🗨️ Hidden from Map'}
                    </button>
                  </div>
                  <div className="mt-2 text-xs text-blue-200">
                    💡 Team vehicles are always visible on the map. This only controls your personal vehicles.
                  </div>
                </div>

                {/* Vehicle Management Controls */}
                <div className="flex flex-col gap-3 mb-4">
                  <div className="flex flex-wrap items-center gap-2">
                    <select
                      value={selectedWeek || ''}
                      onChange={handleWeekChange}
                      className="bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm shadow-lg flex-1 min-w-0"
                    >
                      {availableWeeks.length === 0 ? (
                        <option value="">No weeks available</option>
                      ) : (
                        availableWeeks.map(week => (
                          <option key={week.id} value={week.id}>
                            {week.displayRange || 'Week of ' + new Date(week.startDate).toLocaleDateString()}
                            {week.startDate <= new Date() && week.endDate >= new Date() ? ' (Current)' : ''}
                          </option>
                        ))
                      )}
                    </select>
                  </div>

                  <div className="flex flex-wrap gap-2">
                    <button
                      onClick={checkForMissingCarryovers}
                      disabled={loading || !selectedWeek}
                      className="bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-700 hover:to-yellow-800 disabled:from-gray-600 disabled:to-gray-700 text-white px-3 py-2 rounded-lg shadow-lg flex items-center justify-center font-semibold transition-all disabled:cursor-not-allowed text-sm"
                    >
                      🔄 Carryovers
                    </button>

                    <button
                      onClick={cleanupIncorrectlyAddedVehicles}
                      className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-3 py-2 rounded-lg shadow-lg flex items-center justify-center font-semibold transition-all text-sm"
                    >
                      🧹 Clean
                    </button>

                    <button
                      onClick={() => setShowAddForm(true)}
                      className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-3 py-2 rounded-lg shadow-lg flex items-center justify-center font-semibold transition-all transform hover:scale-105 text-sm"
                    >
                      ➕ Add
                    </button>

                    <button
                      onClick={exportToPDF}
                      disabled={vehicles.length === 0}
                      className={`${
                        vehicles.length === 0 
                          ? 'bg-gray-600 cursor-not-allowed' 
                          : 'bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800'
                      } text-white px-3 py-2 rounded-lg shadow-lg flex items-center justify-center font-semibold transition-all text-sm`}
                    >
                      📄 PDF
                    </button>
                  </div>
                </div>

                {/* Add Vehicle Form */}
                {showAddForm && (
                  <div className="bg-gray-800 bg-opacity-90 p-4 rounded-xl mb-4 border border-gray-600 shadow-xl max-h-96 overflow-y-auto">
                    <div className="flex justify-between items-center mb-3">
                      <h4 className="text-lg font-bold text-blue-300">Add New Vehicle</h4>
                    </div>

                    {/* Get Location Button */}
                    <div className="mb-3">
                      <button
                        onClick={getCurrentLocationAndAddress}
                        disabled={isGettingLocation}
                        className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white py-3 rounded-lg font-bold shadow-lg flex items-center justify-center transition-all transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isGettingLocation ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                            Getting Location...
                          </>
                        ) : (
                          <>📍 Get Location</>
                        )}
                      </button>
                    </div>

                    {/* Compact form */}
                    <div className="grid grid-cols-1 gap-3">
                      <input
                        type="text"
                        name="vehicle"
                        value={newVehicle.vehicle}
                        onChange={handleNewVehicleChange}
                        placeholder="2020 Honda Civic"
                        className="w-full bg-gray-900 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm focus:border-blue-500 focus:outline-none"
                      />
                      <input
                        type="text"
                        name="vin"
                        value={newVehicle.vin}
                        onChange={handleNewVehicleChange}
                        maxLength={6}
                        placeholder="VIN (6 digits)"
                        className="w-full bg-gray-900 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm focus:border-blue-500 focus:outline-none font-mono"
                      />
                      <div className="grid grid-cols-2 gap-2">
                        <input
                          type="text"
                          name="city"
                          value={newVehicle.city}
                          onChange={handleNewVehicleChange}
                          placeholder="City"
                          className="w-full bg-gray-900 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm focus:border-blue-500 focus:outline-none"
                        />
                        <select
                          name="state"
                          value={newVehicle.state}
                          onChange={handleNewVehicleChange}
                          className="w-full bg-gray-900 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm focus:border-blue-500 focus:outline-none"
                        >
                          <option value="">State</option>
                          {STATE_OPTIONS.map(state => (
                            <option key={state.value} value={state.value}>
                              {state.value}
                            </option>
                          ))}
                        </select>
                      </div>
                      <textarea
                        name="notes"
                        value={newVehicle.notes}
                        onChange={handleNewVehicleChange}
                        placeholder="Notes..."
                        rows="2"
                        className="w-full bg-gray-900 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm focus:border-blue-500 focus:outline-none"
                      />
                    </div>

                    <div className="flex gap-2 mt-3">
                      <button
                        onClick={handleAddVehicle}
                        disabled={loading || !newVehicle.vehicle.trim() || !newVehicle.vin.trim()}
                        className="flex-1 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white py-2 rounded-lg font-semibold shadow-lg disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                      >
                        {loading ? 'Adding...' : 'Add Vehicle'}
                      </button>
                      <button
                        onClick={() => {
                          setShowAddForm(false);
                          localStorage.removeItem('vehicleFormDraft');
                          setNewVehicle({
                            vehicle: '',
                            vin: '',
                            vinVerified: false,
                            status: 'PENDING PICKUP',
                            date: new Date().toISOString().split('T')[0],
                            plateNumber: '',
                            accountNumber: '',
                            financier: '',
                            address: '',
                            city: '',
                            state: '',
                            zipCode: '',
                            position: null,
                            notes: '',
                            images: [],
                            color: '',
                            driveType: '',
                            doNotSecureReason: ''
                          });
                        }}
                        className="px-4 bg-gray-600 hover:bg-gray-700 text-white py-2 rounded-lg font-semibold shadow-lg text-sm"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                )}

                {/* Vehicle List */}
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {selectedWeek === null ? (
                    <div className="bg-gray-800 bg-opacity-50 p-4 rounded-xl text-center">
                      <p className="text-gray-400">No week selected</p>
                    </div>
                  ) : vehicles.length === 0 ? (
                    <div className="bg-gray-800 bg-opacity-50 p-4 rounded-xl text-center">
                      <span className="text-3xl mb-2 block">🚗</span>
                      <p className="text-gray-300 font-semibold">No vehicles</p>
                    </div>
                  ) : (
                    vehicles.map((vehicle) => {
                      const isTeamSecured = vehicle.autoSecuredFromTeam && vehicle.securedByTeammate;

                      return (
                        <div 
                          key={vehicle.id} 
                          className={`bg-gray-800 bg-opacity-90 rounded-lg p-3 border ${
                            isTeamSecured ? 'border-purple-500' : 
                            vehicle.bottomStatus ? 'border-red-500' :
                            vehicle.status === 'SECURED' ? 'border-green-500' :
                            vehicle.status === 'FOUND' ? 'border-yellow-500' :
                            'border-gray-700'
                          }`}
                        >
                          {/* Vehicle Header */}
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <h4 className="font-bold text-white text-sm">{vehicle.vehicle}</h4>
                              <div className="flex gap-2 text-xs">
                                <span className="bg-red-800 text-red-200 px-2 py-1 rounded">
                                  VIN: {vehicle.vin}
                                </span>
                                <span className={`px-2 py-1 rounded ${
                                  vehicle.status === 'SECURED' ? 'bg-green-600' :
                                  vehicle.status === 'FOUND' ? 'bg-yellow-600' :
                                  'bg-blue-600'
                                } text-white`}>
                                  {vehicle.status}
                                </span>
                              </div>
                            </div>
                            
                            <div className="flex gap-1">
                              <button
                                onClick={() => handleEditVehicle(vehicle)}
                                className="bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs"
                              >
                                ✏️
                              </button>
                              <select
                                value={vehicle.status}
                                onChange={(e) => handleStatusChange(vehicle.id, e.target.value)}
                                className="bg-gray-700 text-white border border-gray-600 rounded px-2 py-1 text-xs"
                              >
                                <option value="FOUND">Found</option>
                                <option value="SECURED">Secured</option>
                                <option value="NOT FOUND">Not Found</option>
                                <option value="PENDING PICKUP">Pending</option>
                              </select>
                            </div>
                          </div>

                          {/* Address */}
                          {vehicle.fullAddress && (
                            <div className="text-xs text-blue-300 mb-1">
                              📍 {vehicle.fullAddress}
                            </div>
                          )}

                          {/* Notes */}
                          {vehicle.notes && (
                            <div className="text-xs text-gray-300">
                              📝 {vehicle.notes}
                            </div>
                          )}
                        </div>
                      );
                    })
                  )}
                </div>
              </div>
            </div>
          )}

          {/* NEW: Chat Panel */}
          {isFeatureOpen('chat') && (
            <div className="overlay-panel visible">
              <div className="overlay-panel-header">
                <div className="overlay-panel-title">
                  💬 Team Chat
                </div>
                <button 
                  className="overlay-panel-close" 
                  onClick={() => toggleFeature('chat')}
                >
                  ×
                </button>
              </div>
              <div className="overlay-panel-content">
                <div className="chat-container">
                  <div className="text-sm text-gray-300 mb-3">
                    Team: {team?.name || 'Unknown'} • {user?.displayName || 'User'}
                  </div>
                  
                  <div className="chat-messages">
                    {chatLoading ? (
                      <div className="text-center text-gray-400">
                        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto mb-2"></div>
                        Loading messages...
                      </div>
                    ) : chatMessages.length === 0 ? (
                      <div className="text-center text-gray-400">
                        No messages yet. Start the conversation!
                      </div>
                    ) : (
                      chatMessages.map(message => (
                        <div 
                          key={message.id} 
                          className={`chat-message ${message.userId === user.id ? 'own' : ''}`}
                        >
                          <div className="chat-avatar">
                            {message.userName?.charAt(0).toUpperCase() || '?'}
                          </div>
                          <div className="chat-content">
                            <div className="chat-sender">
                              {message.userId === user.id ? 'You' : message.userName}
                            </div>
                            <div className="chat-text">
                              {message.message}
                            </div>
                            <div className="chat-timestamp">
                              {message.timestamp?.toLocaleTimeString() || 'Just now'}
                            </div>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                  
                  <div className="chat-input-container">
                    <textarea
                      value={newChatMessage}
                      onChange={(e) => setNewChatMessage(e.target.value)}
                      placeholder="Type a message..."
                      className="chat-input"
                      rows="1"
                      onKeyPress={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          sendChatMessage();
                        }
                      }}
                    />
                    <button
                      onClick={sendChatMessage}
                      disabled={!newChatMessage.trim()}
                      className="chat-send-btn"
                    >
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <line x1="22" y1="2" x2="11" y2="13"/>
                        <polygon points="22,2 15,22 11,13 2,9"/>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* NEW: Trails Panel */}
          {isFeatureOpen('trails') && (
            <div className="overlay-panel visible">
              <div className="overlay-panel-header">
                <div className="overlay-panel-title">
                  🛤️ Location Trails
                </div>
                <button 
                  className="overlay-panel-close" 
                  onClick={() => toggleFeature('trails')}
                >
                  ×
                </button>
              </div>
              <div className="overlay-panel-content">
                <div className="trails-container">
                  <div className="trails-controls">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="text-white font-semibold">Trail Controls</h4>
                      <button
                        onClick={toggleTrails}
                        className={`trails-toggle-btn ${showTrails ? 'bg-green-600' : 'bg-gray-600'}`}
                      >
                        {showTrails ? '👁️ Hide Trails' : '👁️‍🗨️ Show Trails'}
                      </button>
                    </div>
                    
                    <div>
                      <label className="block text-gray-300 text-sm mb-2">Time Range:</label>
                      <div className="trails-time-selector">
                        {TRAIL_TIME_RANGES.map(range => (
                          <button
                            key={range.label}
                            onClick={() => handleTrailTimeRangeChange(range)}
                            className={`trails-time-btn ${
                              trailTimeRange.label === range.label ? 'active' : ''
                            }`}
                          >
                            {range.label}
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>
                  
                  <div className="trails-stats">
                    <h4 className="text-white font-semibold mb-3">
                      Team Members ({teammateColors?.size || 0})
                    </h4>
                    
                    {trailsLoading ? (
                      <div className="text-center text-gray-400">
                        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto mb-2"></div>
                        Loading trails...
                      </div>
                    ) : teammateColors?.size === 0 ? (
                      <div className="text-center text-gray-400">
                        No team member locations found
                      </div>
                    ) : (
                      <div className="trails-member-list">
                        {Array.from(teammateColors?.entries() || []).map(([userId, color]) => {
                          const location = teammateLocations?.get(userId);
                          const isOnline = location && location.timestamp && 
                            (Date.now() - location.timestamp.getTime()) < 10 * 60 * 1000;
                          const isCurrentUser = userId === user.id;
                          
                          return (
                            <div key={userId} className="trails-member-item">
                              <div className="trails-member-info">
                                <div 
                                  className="trails-member-color"
                                  style={{ backgroundColor: color }}
                                />
                                <div>
                                  <div className="trails-member-name">
                                    {isCurrentUser ? 'You' : (location?.userName || 'Team Member')}
                                  </div>
                                  <div className="trails-member-status">
                                    {isOnline ? '🟢 Online' : '🔴 Offline'}
                                    {location?.timestamp && (
                                      <span className="ml-2">
                                        {Math.round((Date.now() - location.timestamp.getTime()) / (1000 * 60))}m ago
                                      </span>
                                    )}
                                  </div>
                                </div>
                              </div>
                              
                              {isAdmin && !isCurrentUser && (
                                <div className="trails-member-actions">
                                  <button
                                    onClick={() => deleteUserTrails(userId)}
                                    className="trails-action-btn trails-delete-btn"
                                  >
                                    Delete
                                  </button>
                                </div>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    )}
                    
                    {isAdmin && teammateColors?.size > 0 && (
                      <div className="mt-4 pt-3 border-t border-gray-600">
                        <button
                          onClick={deleteAllTrails}
                          className="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg font-semibold"
                        >
                          🗑️ Delete All Team Trails
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Features Selector Modal */}
        {showFeaturesSelector && (
          <div className="features-overlay">
            <div className="features-selector">
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-white mb-2">
                  🎛️ Vehicle Tracker Features
                </h2>
                <p className="text-gray-300 text-sm">
                  Select which panel to display over the map (only one at a time)
                </p>
              </div>

              <div className="features-grid">
                {FEATURES.map(feature => (
                  <div
                    key={feature.id}
                    className={`feature-card ${isFeatureOpen(feature.id) ? 'active' : ''}`}
                    onClick={() => {
                      toggleFeature(feature.id);
                      setShowFeaturesSelector(false);
                    }}
                  >
                    <span className="feature-icon">{feature.icon}</span>
                    <div className="feature-title">{feature.title}</div>
                    <div className="feature-description">{feature.description}</div>
                  </div>
                ))}
              </div>

              <div className="flex justify-center gap-3 mt-6">
                <button
                  onClick={() => {
                    setActiveFeature(null);
                    setShowFeaturesSelector(false);
                  }}
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-semibold transition-all"
                >
                  Close All
                </button>
                <button
                  onClick={() => setShowFeaturesSelector(false)}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-semibold transition-all"
                >
                  Done
                </button>
              </div>

              <div className="mt-4 text-center text-gray-400 text-xs">
                💡 Tip: Only one panel can be open at a time for better mobile experience
              </div>
            </div>
          </div>
        )}

        {/* Vehicle Detail Card Modal */}
        {showVehicleDetail && selectedVehicle && (
          <div className="vehicle-detail-overlay" onClick={() => setShowVehicleDetail(false)}>
            <div className="vehicle-detail-card" onClick={(e) => e.stopPropagation()}>
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-xl font-bold text-white mb-2">
                    🚗 {selectedVehicle.vehicle}
                  </h3>
                  <div className="flex gap-2 text-sm">
                    <span className="bg-red-800 text-red-200 px-2 py-1 rounded">
                      VIN: {selectedVehicle.vin}
                    </span>
                    <span className={`px-2 py-1 rounded text-white ${
                      selectedVehicle.status === 'SECURED' ? 'bg-green-600' :
                      selectedVehicle.status === 'FOUND' ? 'bg-yellow-600' :
                      selectedVehicle.status === 'PENDING PICKUP' ? 'bg-blue-600' :
                      'bg-gray-600'
                    }`}>
                      {selectedVehicle.status}
                    </span>
                    {selectedVehicle.isOwnVehicle ? (
                      <span className="bg-blue-600 text-white px-2 py-1 rounded text-xs">
                        Your Vehicle
                      </span>
                    ) : (
                      <span className="bg-purple-600 text-white px-2 py-1 rounded text-xs">
                        {selectedVehicle.teamMemberName}
                      </span>
                    )}
                  </div>
                </div>
                <button
                  onClick={() => setShowVehicleDetail(false)}
                  className="bg-red-600 hover:bg-red-700 text-white rounded-full w-8 h-8 flex items-center justify-center text-lg font-bold"
                >
                  ×
                </button>
              </div>

              <div className="space-y-3">
                {/* Vehicle Details */}
                <div className="bg-gray-800 bg-opacity-50 rounded-lg p-3">
                  <h4 className="font-semibold text-blue-300 mb-2">📋 Vehicle Details</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div><span className="text-gray-400">Date:</span> {selectedVehicle.date}</div>
                    <div><span className="text-gray-400">Color:</span> {selectedVehicle.color || 'N/A'}</div>
                    <div><span className="text-gray-400">Drive Type:</span> {selectedVehicle.driveType || 'N/A'}</div>
                    <div><span className="text-gray-400">Plate #:</span> {selectedVehicle.plateNumber || 'N/A'}</div>
                    <div><span className="text-gray-400">Account #:</span> {selectedVehicle.accountNumber || 'N/A'}</div>
                    <div><span className="text-gray-400">Financier:</span> {selectedVehicle.financier || 'N/A'}</div>
                  </div>
                </div>

                {/* Address */}
                {selectedVehicle.fullAddress && (
                  <div className="bg-gray-800 bg-opacity-50 rounded-lg p-3">
                    <h4 className="font-semibold text-blue-300 mb-2">📍 Location</h4>
                    <p className="text-sm text-gray-300">{selectedVehicle.fullAddress}</p>
                    {selectedVehicle.position && (
                      <p className="text-xs text-gray-500 mt-1">
                        GPS: {selectedVehicle.position.lat.toFixed(6)}, {selectedVehicle.position.lng.toFixed(6)}
                      </p>
                    )}
                  </div>
                )}

                {/* Notes */}
                {selectedVehicle.notes && (
                  <div className="bg-gray-800 bg-opacity-50 rounded-lg p-3">
                    <h4 className="font-semibold text-blue-300 mb-2">📝 Notes</h4>
                    <p className="text-sm text-gray-300">{selectedVehicle.notes}</p>
                  </div>
                )}

                {/* Team Information */}
                <div className="bg-gray-800 bg-opacity-50 rounded-lg p-3">
                  <h4 className="font-semibold text-blue-300 mb-2">👥 Team Information</h4>
                  <div className="text-sm space-y-1">
                    <div><span className="text-gray-400">Owner:</span> {selectedVehicle.teamMemberName}</div>
                    <div><span className="text-gray-400">Week:</span> {selectedVehicle.weekRange}</div>
                    {selectedVehicle.carriedOver && (
                      <div className="text-orange-300">
                        📦 Carried over from previous week
                      </div>
                    )}
                    {selectedVehicle.autoSecuredFromTeam && (
                      <div className="text-purple-300">
                        🎉 Auto-secured from team sync
                      </div>
                    )}
                  </div>
                </div>

                {/* Images */}
                {selectedVehicle.images && selectedVehicle.images.length > 0 && (
                  <div className="bg-gray-800 bg-opacity-50 rounded-lg p-3">
                    <h4 className="font-semibold text-blue-300 mb-2">📸 Images ({selectedVehicle.images.length})</h4>
                    <div className="grid grid-cols-3 gap-2">
                      {selectedVehicle.images.map((image, index) => (
                        <div key={index} className="aspect-square bg-gray-700 rounded-lg overflow-hidden">
                          <img
                            src={image.url || image}
                            alt={`Vehicle ${index + 1}`}
                            className="w-full h-full object-cover cursor-pointer hover:opacity-75 transition-opacity"
                            onClick={() => showImageInModal(image.url || image)}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex gap-2 mt-4">
                  <button
                    onClick={() => setShowVehicleDetail(false)}
                    className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg font-semibold transition-all"
                  >
                    Close
                  </button>
                  {selectedVehicle.fullAddress && (
                    <button
                      onClick={() => {
                        const address = encodeURIComponent(selectedVehicle.fullAddress);
                        window.open(`https://www.google.com/maps/search/${address}`, '_blank');
                      }}
                      className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-semibold transition-all"
                    >
                      🗺️ Navigate
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Image Modal */}
        {showImageModal && selectedImage && (
          <div 
            className="fixed inset-0 bg-black bg-opacity-95 z-50 flex items-center justify-center p-2"
            onClick={() => {
              setShowImageModal(false);
              setSelectedImage(null);
            }}
          >
            <div className="relative max-w-full max-h-full">
              <img 
                src={selectedImage} 
                alt="Vehicle"
                className="max-w-full max-h-[90vh] rounded-lg shadow-2xl"
                style={{ maxHeight: '90vh', width: 'auto', height: 'auto' }}
                onClick={(e) => e.stopPropagation()}
              />
              <button
                onClick={() => {
                  setShowImageModal(false);
                  setSelectedImage(null);
                }}
                className={`absolute top-2 right-2 bg-red-600 text-white rounded-full ${isMobile ? 'w-12 h-12' : 'w-10 h-10'} flex items-center justify-center hover:bg-red-700 shadow-lg text-xl`}
              >
                ✕
              </button>
            </div>
          </div>
        )}

        {/* Print View (visible only when printing) */}
        {showPrintView && <PrintView />}
      </div>

      {/* Print-specific styles */}
      <style type="text/css" media="print">
        {`
        @page { size: landscape; margin: 0.5in; }

        /* Hide regular UI during print */
        body > *:not(.print-container) {
          display: none !important;
        }

        /* Show print container */
        .print-container {
          display: block !important;
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
        }

        /* Print-specific styling */
        .print-container {
          background-color: white !important;
          color: black !important;
        }

        .print-container table {
          width: 100%;
          border-collapse: collapse;
        }

        .print-container th,
        .print-container td {
          border: 1px solid #ddd;
          padding: 8px;
          text-align: left;
        }

        .print-container th {
          background-color: #f2f2f2;
          font-weight: bold;
        }

        .print-container tr:nth-child(even) {
          background-color: #f9f9f9;
        }
        `}
      </style>
    </>
  );
}

export default StandaloneVehicleTracker;